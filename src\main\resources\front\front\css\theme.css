/*定义全局css - 优雅简约设计*/
body {
    /* 优雅配色方案 - 基于自然和谐的色彩 */
    /* 主色调：优雅的深青色 */
    --publicMainColor: #1a365d;
    /* 副色调：清新的青绿色 */
    --publicSubColor: #38a169;
    /* 辅助色：温暖的橙色 */
    --accentColor: #ed8936;
    /* 背景色：纯净白色 */
    --backgroundColor: #ffffff;
    /* 次要背景色：极浅灰 */
    --secondaryBg: #f7fafc;
    /* 文字色：深灰 */
    --textColor: #2d3748;
    /* 次要文字色：中灰 */
    --textSecondary: #718096;
    /* 边框色：浅灰 */
    --borderColor: #e2e8f0;
    /* 阴影色 */
    --shadowColor: rgba(0, 0, 0, 0.1);

    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
    background-color: var(--backgroundColor);
    color: var(--textColor);
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/*开始==================================优雅导航栏设计=========================================开始*/
#iframe {
    width: 100%;
    margin-top: 80px;
    padding: 0;
    min-height: calc(100vh - 80px);
    background: var(--secondaryBg);
}

#header {
	height: auto;
	background: var(--backgroundColor);
	border-bottom: 1px solid var(--borderColor);
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	z-index: 1000;
	box-shadow: 0 2px 20px var(--shadowColor);
	backdrop-filter: blur(10px);
}

#header .nav-top {
	display: flex;
	align-items: center;
	padding: 0 40px;
	font-size: 14px;
	color: var(--textSecondary);
	box-sizing: border-box;
	height: 40px;
	background: var(--secondaryBg);
	justify-content: space-between;
	border-bottom: 1px solid var(--borderColor);
}

#header .nav-top-img {
	width: 124px;
	height: 40px;
	padding: 0;
	margin: 0;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	box-shadow: 0 0 6px rgba(0,0,0,.3);
}

#header .nav-top-title {
	line-height: 40px;
	font-size: 18px;
	color: var(--publicMainColor);
	padding: 0;
	margin: 0;
	font-weight: 600;
	letter-spacing: 0.5px;
}

#header .nav-top-tel {
	line-height: 40px;
	font-size: 13px;
	color: var(--textSecondary);
	padding: 0;
	margin: 0;
}

#header .navs {
	display: flex;
	padding: 0 40px;
	align-items: center;
	box-sizing: border-box;
	height: 40px;
	background: var(--backgroundColor);
	justify-content: center;
	gap: 8px;
}
#header .navs .title {
	width: auto;
	line-height: 40px;
	font-size: 16px;
	color: #333;
	padding: 0 10px;
	margin: 0 5px;
	border-radius: 6px;
	border-width: 0;
	border-style: solid;
	border-color: rgba(0,0,0,.3);
	box-shadow: 0 0 6px rgba(0,0,0,0);
}
#header .navs li {
	display: inline-block;
	width: auto;
	line-height: 32px;
	padding: 0 16px;
	margin: 0 2px;
	color: var(--textColor);
	font-size: 14px;
	font-weight: 500;
	border-radius: 6px;
	background: transparent;
	text-align: center;
	transition: all 0.2s ease;
	cursor: pointer;
	position: relative;
}

#header .navs li a{
	color: inherit;
	text-decoration: none;
	display: flex;
	align-items: center;
	gap: 6px;
	padding: 0;
}

#header .navs li a i {
	font-size: 12px;
	opacity: 0.8;
}

#header .navs li.current a{
	color: var(--publicSubColor);
}

#header .navs li a:hover{
	color: inherit;
}

#header .navs li.current {
	background: rgba(56, 161, 105, 0.1);
	color: var(--publicSubColor);
}

#header .navs li:hover {
	background: var(--secondaryBg);
	color: var(--publicSubColor);
}
/*结束==================================导航栏样式3=========================================结束*/

/*home页面数据样式 开始*/
/*home页面数据样式 结束*/

/*list页面数据样式 开始*/
	/*list页面数据样式 普通数据样式 开始*/
.gallery-demo {
	box-shadow: 0 3px 5px -1px rgba(0, 0, 0, 0.07), 0 5px 8px 0 rgba(0, 0, 0, 0.05), 0 1px 14px 0 rgba(0, 0, 0, 0.04);
	transition: 0.5s all;
	-webkit-transition: 0.5s all;
	-moz-transition: 0.5s all;
	-o-transition: 0.5s all;
	-ms-transition: 0.5s all;
	margin-top: 40px;
}

.gallery-demo:hover {
	box-shadow: 0 12px 16px 0 rgba(0, 0, 0, .24), 0 17px 50px 0 rgba(0, 0, 0, .19);
	transition: 0.5s all;
	-webkit-transition: 0.5s all;
	-moz-transition: 0.5s all;
	-o-transition: 0.5s all;
	-ms-transition: 0.5s all;
}

h4.p-mask {
	color: #636363;
	font-size: 20px;
	letter-spacing: 1px;
	text-align: center;
	padding: 14px 10px 20px;
}

h4.p-mask span {
	color: #191919;
	font-size: 24px
}
	/*list页面数据样式 普通数据样式 结束*/
/*list页面数据样式 结束*/


/* 主页 轮播图选择框颜色 主*/
#test1 .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}
/* 个人中心轮播图 */
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}

/* 大部分颜色 主 */
.main_color {
	color: var(--publicMainColor, #808080);
}
/* 边框颜色 主 */
.main_borderColor{
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 6px var(--publicMainColor, #808080);
}
/* 背景颜色 主 */
.main_backgroundColor {
	background-color: var(--publicMainColor, #808080);
}
/* 登录页面单选按钮颜色 主 */
.l-redio .layui-form-radioed>i {
	font-size: 16px;
	color: var(--publicMainColor, #808080);
}
.l-redio .layui-form-radioed>div {
	font-size: 14px;
	color: var(--publicMainColor, #808080);
}

/* 大部分颜色 副 */
.sub_color {
	color: var(--publicSubColor, #808080);
}
/* 边框颜色 副 */
.sub_borderColor{
	border-color: var(--publicSubColor, #808080);
	box-shadow: 0 0 6px var(--publicSubColor, #808080);
}
/* 背景颜色 副 */
.sub_backgroundColor {
	background-color: var(--publicSubColor, #808080);
}

/* 分页颜色 */
.layui-laypage .layui-laypage-curr .layui-laypage-em {
	background-color: var(--publicMainColor, #808080);
}

/* 评论和简介背景颜色 */
.detail-tab .layui-tab-card>.layui-tab-title .layui-this {
	background-color: var(--publicMainColor, #808080);
	color: #fff;
	font-size: 14px;
}
#swiper .layui-carousel-ind li.layui-this {
	background-color: var(--publicMainColor, #808080);
}

/* 个人中心 菜单点击颜色*/
.center-container .layui-nav-tree .layui-nav-item.layui-this {
	background-color: var(--publicSubColor, #808080);
}
/*个人中心 菜单鼠标移上颜色*/
.center-container .layui-nav-tree .layui-nav-item:hover {
	background-color:var(--publicMainColor, #808080);
}
/*个人中心 菜单下线颜色*/
.center-container .layui-nav-tree .layui-nav-item {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 输入框中字体颜色和边框颜色*/
.right-container .input .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 下拉框中字体颜色和边框颜色*/
.right-container .select .layui-input {
	color: var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}
/*个人中心 未知颜色*/
.right-container .date .layui-input {
	border-color: var(--publicMainColor, #808080);
	box-shadow: 0 0 0px var(--publicMainColor, #808080);
}

/* 前台elementUI得下拉框内容颜色和边框颜色修改 */
/* start */
.el-select-dropdown__item.selected {
	color: var(--publicMainColor, #808080);
	font-weight: bold;
}
.el-select .el-input.is-focus .el-input__inner {
	border-color: var(--publicMainColor, #808080);
}
.el-input--suffix .el-input__inner{
	color:var(--publicMainColor, #808080);
	border-color: var(--publicMainColor, #808080);
}
.el-select .el-input__inner:focus {
	border-color: var(--publicMainColor, #808080);
}
/* end */
/*=====================富文本框字体样式===========================================================================*/

.ql-size-small {
	font-size: 10px;
}
.ql-size-large {
	font-size: 18px;
}
.ql-size-huge {
	font-size: 32px;
}