# 商家删除功能修复说明

## 问题描述
管理员删除商家时，只是将商家的 `shangjia_delete` 字段设置为 2（逻辑删除），但没有处理商家相关的数据，导致数据库中存在大量孤立数据。用户要求实现物理删除，即直接从数据库中删除商家记录。

## 问题分析
商家相关的数据包括：
1. **商品** (`shangpin`) - 通过 `shangjia_id` 直接关联，有 `shangpin_delete` 字段
2. **商品订单** (`shangpin_order`) - 通过商品间接关联，没有删除标记字段
3. **购物车** (`cart`) - 通过商品间接关联，没有删除标记字段
4. **商品收藏** (`shangpin_collection`) - 通过商品间接关联，没有删除标记字段
5. **商品评价** (`shangpin_commentback`) - 通过商品间接关联，没有删除标记字段

## 修复方案
修改 `ShangjiaController` 的删除方法，添加级联删除逻辑：

### 修复步骤：
1. **添加依赖注入** - 注入相关的Service类
2. **实现级联删除逻辑** - 按照正确的顺序删除相关数据
3. **保持数据一致性** - 确保删除操作的原子性

### 具体实现：
```java
// 1. 查询该商家的所有商品
List<ShangpinEntity> shangpinList = shangpinService.selectList(
    new EntityWrapper<ShangpinEntity>()
        .eq("shangjia_id", shangjiaId)
        .eq("shangpin_delete", 1)
);

if(shangpinList != null && shangpinList.size() > 0){
    // 获取所有商品ID
    List<Integer> shangpinIds = new ArrayList<>();
    for(ShangpinEntity shangpin : shangpinList){
        shangpinIds.add(shangpin.getId());
    }

    // 2. 删除相关的购物车数据
    cartService.delete(new EntityWrapper<CartEntity>()
        .in("shangpin_id", shangpinIds)
    );

    // 3. 删除相关的商品收藏数据
    shangpinCollectionService.delete(new EntityWrapper<ShangpinCollectionEntity>()
        .in("shangpin_id", shangpinIds)
    );

    // 4. 删除相关的商品评价数据
    shangpinCommentbackService.delete(new EntityWrapper<ShangpinCommentbackEntity>()
        .in("shangpin_id", shangpinIds)
    );

    // 5. 删除相关的商品订单数据
    shangpinOrderService.delete(new EntityWrapper<ShangpinOrderEntity>()
        .in("shangpin_id", shangpinIds)
    );

    // 6. 物理删除该商家的所有商品
    shangpinService.deleteBatchIds(shangpinIds);
}

// 7. 最后物理删除商家（直接从数据库中删除记录）
shangjiaService.deleteBatchIds(Arrays.asList(ids));
```

## 修复文件
- `src\main\java\com\controller\ShangjiaController.java`

## 手动测试步骤

### 测试前准备：
1. 启动应用程序
2. 登录管理员账户
3. 创建测试数据：
   - 创建一个测试商家
   - 为该商家添加商品
   - 创建用户并添加购物车、收藏、评价等数据

### 测试步骤：
1. **查看删除前的数据**
   ```sql
   -- 查看商家信息
   SELECT * FROM shangjia WHERE id = [商家ID];

   -- 查看商家的商品
   SELECT * FROM shangpin WHERE shangjia_id = [商家ID];

   -- 查看相关的购物车数据
   SELECT * FROM cart WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );

   -- 查看相关的收藏数据
   SELECT * FROM shangpin_collection WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );

   -- 查看相关的评价数据
   SELECT * FROM shangpin_commentback WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );

   -- 查看相关的订单数据
   SELECT * FROM shangpin_order WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );
   ```

2. **执行删除操作**
   - 在管理员界面中删除该商家

3. **验证删除结果**
   ```sql
   -- 验证商家被物理删除
   SELECT COUNT(*) FROM shangjia WHERE id = [商家ID];
   -- 应该返回 0

   -- 验证商品被物理删除
   SELECT COUNT(*) FROM shangpin WHERE shangjia_id = [商家ID];
   -- 应该返回 0

   -- 验证相关数据被物理删除
   SELECT COUNT(*) FROM cart WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );
   -- 应该返回 0

   SELECT COUNT(*) FROM shangpin_collection WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );
   -- 应该返回 0

   SELECT COUNT(*) FROM shangpin_commentback WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );
   -- 应该返回 0

   SELECT COUNT(*) FROM shangpin_order WHERE shangpin_id IN (
       SELECT id FROM shangpin WHERE shangjia_id = [商家ID]
   );
   -- 应该返回 0
   ```

## 预期结果
- 商家被物理删除（从数据库中完全删除）
- 商家的所有商品被物理删除（从数据库中完全删除）
- 相关的购物车、收藏、评价、订单数据被物理删除
- 数据库中不再有任何相关数据

## 注意事项
1. **删除操作是完全不可逆的**，请谨慎操作
2. **强烈建议在生产环境中先备份数据库**
3. 一旦删除，所有相关数据都无法恢复
4. 物理删除比逻辑删除风险更高，请确保真的需要完全删除

## 技术细节
- 使用了MyBatis-Plus的批量操作提高性能
- 改为物理删除机制，直接从数据库中删除记录
- 添加了完整的级联删除逻辑
- 确保了数据的一致性和完整性
- 使用 `deleteBatchIds()` 方法实现批量物理删除
