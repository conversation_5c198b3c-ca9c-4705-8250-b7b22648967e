<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>登录</title>
    <link rel="stylesheet" type="text/css" href="../../layui/css/layui.css">
    <link rel="stylesheet" type="text/css" href="../../xznstatic/css/public.css"/>
    <link rel="stylesheet" href="../../css/theme.css"/>
    <link rel="stylesheet" href="../../css/modern-style.css"/>
    <!-- 引入优雅图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>

    <style type="text/css">
        .login {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            height: 100vh;
            background: var(--secondaryBg);
            position: relative;
        }

        .login form {
            box-sizing: border-box;
            min-height: 480px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            background: var(--backgroundColor);
            border-radius: 16px;
            box-shadow: 0 8px 32px var(--shadowColor);
            border: 1px solid var(--borderColor);
            width: 400px;
            max-width: 90vw;
            padding: 40px;
        }
        .login .logo, .login .title {
            box-sizing: border-box;
        }
        .login .logo img {
            display: block;
        }
        .login .title {
            text-align: center;
        }
        .login .form-item {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            box-sizing: border-box;
        }
        .login .form-item input, .login .form-label {
            box-sizing: border-box;
        }
        .login .btn-submit {
            display: block;
            box-sizing: border-box;
        }
        .login form p.txt {
            width: 100%;
            margin: 0;
            box-sizing: border-box;
        }
        .l-redio .layui-form-radio {
            margin: 0;
        }


        #loginForm .codes {
            display: none;
        }

        #loginForm .codes input {
            width: calc(100% - 84px);
            height: 44px;
            margin: 0;
            color: #1e90ff;
            font-size: 14px;
            padding: 0 10px;
            border-radius: 0;
            border-width: 1px;
            border-style: solid;
            border-color: #1e90ff;
            background-color: #fff;
            box-shadow: 0 0 6px rgba(30, 144, 255, 0);
            outline: none;
        }

        #loginForm .codes .nums {
            width: 84px;
            height: 44px;
            margin: 0;
            color: $template2.front.login.code.nums.color;
            font-size: $template2.front.login.code.nums.fontSize;
            padding: 0;
            border-radius: 0;
            border-width: 1px 1px 1px 0;
            border-style: solid;
            border-color: #1e90ff;
            background-color: #f5f5f5;
            box-shadow: 0 0 6px rgba(30, 144, 255, 0);
            outline: none;
            box-sizing: border-box;
        }

    </style>
</head>
<body>
<div id="app" class="login">
    <form id="loginForm" class="layui-form login-form">
        <!-- 优雅标题区域 -->
        <div style="text-align: center; margin-bottom: 32px;">
            <div style="display: inline-flex; align-items: center; justify-content: center; width: 64px; height: 64px; background: var(--publicSubColor); border-radius: 12px; margin-bottom: 16px;">
                <i data-lucide="user" style="width: 32px; height: 32px; color: #fff;"></i>
            </div>
            <h1 style="margin: 0; font-size: 24px; font-weight: 600; color: var(--textColor); margin-bottom: 8px;">
                用户登录
            </h1>
            <p style="margin: 0; color: var(--textSecondary); font-size: 14px;">
                欢迎回到多角色商业服务系统
            </p>
        </div>

        <div style="background: var(--secondaryBg); padding: 12px 16px; border-radius: 8px; margin-bottom: 24px; border-left: 3px solid var(--publicSubColor);">
            <div style="display: flex; align-items: center; color: var(--textSecondary); font-size: 13px;">
                <i data-lucide="info" style="width: 14px; height: 14px; margin-right: 8px;"></i>
                公共场所不建议自动登录，以防账号丢失
            </div>
        </div>
        <!-- 账号输入框 -->
        <div class="form-item" style="margin-bottom: 20px; position: relative;">
            <label style="display: block; margin-bottom: 6px; color: var(--textColor); font-size: 14px; font-weight: 500;">账号</label>
            <div style="position: relative;">
                <i data-lucide="user" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: var(--textSecondary); width: 16px; height: 16px;"></i>
                <input style="width: 100%; padding: 12px 12px 12px 40px; border: 1px solid var(--borderColor); border-radius: 8px; font-size: 14px; transition: all 0.2s ease; background: var(--backgroundColor);" type="text" name="username" required lay-verify="required" placeholder="请输入账号" autocomplete="off">
            </div>
        </div>

        <!-- 密码输入框 -->
        <div class="form-item" style="margin-bottom: 24px; position: relative;">
            <label style="display: block; margin-bottom: 6px; color: var(--textColor); font-size: 14px; font-weight: 500;">密码</label>
            <div style="position: relative;">
                <i data-lucide="lock" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: var(--textSecondary); width: 16px; height: 16px;"></i>
                <input style="width: 100%; padding: 12px 12px 12px 40px; border: 1px solid var(--borderColor); border-radius: 8px; font-size: 14px; transition: all 0.2s ease; background: var(--backgroundColor);" type="password" name="password" required lay-verify="required" placeholder="请输入密码" autocomplete="off">
            </div>
        </div>
        <div class="form-item codes" :style='{"padding":"0","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"0 auto","borderColor":"rgba(0,0,0,1)","backgroundColor":"#fff","borderRadius":"0","borderWidth":"0 0 1px 0","width":"80%","borderStyle":"solid","height":"64px"}'>
            <input style="flex: 1;" type="text" id="code" placeholder="请输入验证码">
            <div class="nums" id="nums" style="display: flex;justify-content: center;align-items: center;">
            </div>
        </div>
        <!-- 登录按钮 -->
        <button class="elegant-btn" style="width: 100%; height: 44px; font-size: 14px; font-weight: 500; margin: 24px 0 16px 0; justify-content: center;" lay-submit lay-filter="login">
            <i data-lucide="log-in" style="width: 16px; height: 16px; margin-right: 6px;"></i>
            立即登录
        </button>

        <!-- 注册链接 -->
        <div style="text-align: center; margin-top: 16px;">
            <p style="margin: 0; color: var(--textSecondary); font-size: 14px;">
                还没有账号？
                <a href="javascript:registerClick('yonghu')" style="color: var(--publicSubColor); text-decoration: none; font-weight: 500; margin-left: 4px;">
                    立即注册
                </a>
            </p>
        </div>
    </form>
</div>

<script src="../../layui/layui.js"></script>
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">
<!-- 组件配置信息 -->
<script src="../../js/config.js"></script>
<!-- 扩展插件配置信息 -->
<script src="../../modules/config.js"></script>
<!-- 工具方法 -->
<script src="../../js/utils.js"></script>
<script type="text/javascript">
    var vue = new Vue({
        el: '#app',
        data: {
            menu: menu
        },
        mounted() {
            // 初始化Lucide图标
            this.$nextTick(() => {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            });
        },
        methods: {
            jump(url) {
                jump(url)
            }
        }
    })
    var codes = [{
        num: 1,
        color: '#000',
        rotate: '10deg',
        size: '16px'
    }, {
        num: 2,
        color: '#000',
        rotate: '10deg',
        size: '16px'
    }, {
        num: 3,
        color: '#000',
        rotate: '10deg',
        size: '16px'
    }, {
        num: 4,
        color: '#000',
        rotate: '10deg',
        size: '16px'
    }]


    layui.use(['layer', 'element', 'carousel', 'form', 'http', 'jquery'], function() {
        var layer = layui.layer;
        var element = layui.element;
        var carousel = layui.carousel;
        var form = layui.form;
        var http = layui.http;
        var jquery = layui.jquery;

        function randomString() {
            var len = 4;
            var chars = [
                'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
                'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
                'w', 'x', 'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G',
                'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R',
                'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '0', '1', '2',
                '3', '4', '5', '6', '7', '8', '9'
            ]
            var colors = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f']
            var sizes = ['14', '15', '16', '17', '18']

            var output = []
            for (var i = 0; i < len; i++) {
                // 随机验证码
                var key = Math.floor(Math.random() * chars.length)
                codes[i].num = chars[key]
                // 随机验证码颜色
                var code = '#'
                for (var j = 0; j < 6; j++) {
                    var key = Math.floor(Math.random() * colors.length)
                    code += colors[key]
                }
                codes[i].color = code
                // 随机验证码方向
                var rotate = Math.floor(Math.random() * 45)
                var plus = Math.floor(Math.random() * 2)
                if (plus == 1) rotate = '-' + rotate
                codes[i].rotate = 'rotate(' + rotate + 'deg)'
                // 随机验证码字体大小
                var size = Math.floor(Math.random() * sizes.length)
                codes[i].size = sizes[size] + 'px'
            }

            var str = ''
            for(var i = 0;i<codes.length;i++) {
                str += '<span style="color:' + codes[i].color + ';transform:' + codes[i].rotate + ';fontSize:' + codes[i].size + ';padding: 0 3px;display:inline-block">'+codes[i].num+'</span>'
            }
            jquery('#nums').html('').append(str);
        }

        jquery('#nums').click(function(){
            randomString();
        })

        randomString();

        // 登录
        form.on('submit(login)', function(data) {


            data = data.field;
            data.role ='yonghu';
            if(false) {
                var arr = []
                for(var i = 0;i<codes.length;i++) {
                    arr.push(codes[i].num)
                }
                if(arr.join('').toLowerCase() != jquery('#code').val().toLowerCase()) {
                    alert("请输入正确的验证码");
                    randomString()

                    return false;
                }
            }
            http.request(data.role + '/login', 'get', data, function(res) {
                layer.msg('登录成功', {
                    time: 2000,
                    icon: 6
                });
                // 登录凭证
                localStorage.setItem('Token', res.token);
                localStorage.setItem('role', jquery('#role:checked').attr('title'));
                // 当前登录用户角色
                localStorage.setItem('userTable', data.role);
                localStorage.setItem('sessionTable', data.role);
                // 用户名称
                localStorage.setItem('adminName', data.username);
                http.request(data.role + '/session', 'get', {}, function(res) {
                    // 用户id
                    localStorage.setItem('userid', res.data.id);
                    // 路径访问设置
                    window.location.href = '../../index.html';
                })

            });
            return false
        });

    });

    /**
     * 跳转登录
     * @param {Object} tablename
     */
    function registerClick(tablename) {
        window.location.href = '../' + tablename + '/register.html?tablename=' + tablename;
    }



</script>
</body>
</html>
