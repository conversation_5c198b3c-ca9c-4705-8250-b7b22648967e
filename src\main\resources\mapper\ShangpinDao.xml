<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dao.ShangpinDao">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        a.id as id
        ,a.shangjia_id as shangjiaId
        ,a.shangpin_name as shangpinName
        ,a.shangpin_photo as shangpinPhoto
        ,a.shangpin_types as shangpinTypes
        ,a.shangpin_kucun_number as shangpinKucunNumber
        ,a.shangpin_old_money as shangpinOldMoney
        ,a.shangpin_new_money as shangpinNewMoney
        ,a.shangpin_clicknum as shangpinClicknum
        ,a.shangpin_content as shangpinContent
        ,a.shangxia_types as shangxiaTypes
        ,a.shangpin_delete as shangpinDelete
        ,a.create_time as createTime
    </sql>
    <select id="selectListView" parameterType="map" resultType="com.entity.view.ShangpinView" >
        SELECT
        <include refid="Base_Column_List" />

--         级联表的字段
        ,shangjia.shangjia_name as shangjiaName
        ,shangjia.shangjia_phone as shangjiaPhone
        ,shangjia.shangjia_email as shangjiaEmail
        ,shangjia.shangjia_photo as shangjiaPhoto
        ,shangjia.shangjia_address as shangjiaAddress
        ,shangjia.shangjia_xingji_types as shangjiaXingjiTypes
        ,shangjia.shangjia_yesno_types as shangjiaYesnoTypes
        ,shangjia.new_money as newMoney
        ,shangjia.shangjia_content as shangjiaContent
        ,shangjia.shangjia_delete as shangjiaDelete

        FROM shangpin  a
        left JOIN shangjia shangjia ON a.shangjia_id = shangjia.id

        <where>
            <if test="params.ids != null">
                and a.id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                #{item}
                </foreach>
            </if>
            <if test="params.shangjiaId != null and params.shangjiaId != ''">
                and (
                    a.shangjia_id = #{params.shangjiaId}
                )
            </if>
            <if test=" params.shangpinName != '' and params.shangpinName != null and params.shangpinName != 'null' ">
                and a.shangpin_name like CONCAT('%',#{params.shangpinName},'%')
            </if>
            <if test="params.shangpinTypes != null and params.shangpinTypes != ''">
                and a.shangpin_types = #{params.shangpinTypes}
            </if>
            <if test="params.shangpinKucunNumberStart != null and params.shangpinKucunNumberStart != ''">
                <![CDATA[  and a.shangpin_kucun_number >= #{params.shangpinKucunNumberStart}   ]]>
            </if>
            <if test="params.shangpinKucunNumberEnd != null and params.shangpinKucunNumberEnd != ''">
                <![CDATA[  and a.shangpin_kucun_number <= #{params.shangpinKucunNumberEnd}   ]]>
            </if>
             <if test="params.shangpinKucunNumber != null and params.shangpinKucunNumber != ''">
                and a.shangpin_kucun_number = #{params.shangpinKucunNumber}
             </if>
            <if test="params.shangpinOldMoneyStart != null ">
                <![CDATA[  and a.shangpin_old_money >= #{params.shangpinOldMoneyStart}   ]]>
            </if>
            <if test="params.shangpinOldMoneyEnd != null ">
                <![CDATA[  and a.shangpin_old_money <= #{params.shangpinOldMoneyEnd}   ]]>
            </if>
            <if test="params.shangpinNewMoneyStart != null ">
                <![CDATA[  and a.shangpin_new_money >= #{params.shangpinNewMoneyStart}   ]]>
            </if>
            <if test="params.shangpinNewMoneyEnd != null ">
                <![CDATA[  and a.shangpin_new_money <= #{params.shangpinNewMoneyEnd}   ]]>
            </if>
            <if test="params.shangpinClicknumStart != null and params.shangpinClicknumStart != ''">
                <![CDATA[  and a.shangpin_clicknum >= #{params.shangpinClicknumStart}   ]]>
            </if>
            <if test="params.shangpinClicknumEnd != null and params.shangpinClicknumEnd != ''">
                <![CDATA[  and a.shangpin_clicknum <= #{params.shangpinClicknumEnd}   ]]>
            </if>
             <if test="params.shangpinClicknum != null and params.shangpinClicknum != ''">
                and a.shangpin_clicknum = #{params.shangpinClicknum}
             </if>
            <if test=" params.shangpinContent != '' and params.shangpinContent != null and params.shangpinContent != 'null' ">
                and a.shangpin_content like CONCAT('%',#{params.shangpinContent},'%')
            </if>
            <if test="params.shangxiaTypes != null and params.shangxiaTypes != ''">
                and a.shangxia_types = #{params.shangxiaTypes}
            </if>
            <if test="params.shangpinDeleteStart != null and params.shangpinDeleteStart != ''">
                <![CDATA[  and a.shangpin_delete >= #{params.shangpinDeleteStart}   ]]>
            </if>
            <if test="params.shangpinDeleteEnd != null and params.shangpinDeleteEnd != ''">
                <![CDATA[  and a.shangpin_delete <= #{params.shangpinDeleteEnd}   ]]>
            </if>
             <if test="params.shangpinDelete != null and params.shangpinDelete != ''">
                and a.shangpin_delete = #{params.shangpinDelete}
             </if>

                <!-- 判断商家的id不为空 -->
            <if test=" params.shangjiaIdNotNull != '' and params.shangjiaIdNotNull != null and params.shangjiaIdNotNull != 'null' ">
                and a.shangjia_id IS NOT NULL
            </if>
            <if test=" params.shangjiaName != '' and params.shangjiaName != null and params.shangjiaName != 'null' ">
                and shangjia.shangjia_name like CONCAT('%',#{params.shangjiaName},'%')
            </if>
            <if test=" params.shangjiaPhone != '' and params.shangjiaPhone != null and params.shangjiaPhone != 'null' ">
                and shangjia.shangjia_phone like CONCAT('%',#{params.shangjiaPhone},'%')
            </if>
            <if test=" params.shangjiaEmail != '' and params.shangjiaEmail != null and params.shangjiaEmail != 'null' ">
                and shangjia.shangjia_email like CONCAT('%',#{params.shangjiaEmail},'%')
            </if>
            <if test=" params.shangjiaAddress != '' and params.shangjiaAddress != null and params.shangjiaAddress != 'null' ">
                and shangjia.shangjia_address like CONCAT('%',#{params.shangjiaAddress},'%')
            </if>
            <if test="params.shangjiaXingjiTypes != null  and params.shangjiaXingjiTypes != ''">
                and shangjia.shangjia_xingji_types = #{params.shangjiaXingjiTypes}
            </if>

            <if test="params.shangjiaYesnoTypes != null  and params.shangjiaYesnoTypes != ''">
                and shangjia.shangjia_yesno_types = #{params.shangjiaYesnoTypes}
            </if>

            <if test="params.newMoneyStart != null ">
                <![CDATA[  and shangjia.new_money >= #{params.newMoneyStart}   ]]>
            </if>
            <if test="params.newMoneyEnd != null ">
                <![CDATA[  and shangjia.new_money <= #{params.newMoneyEnd}   ]]>
            </if>
            <if test=" params.shangjiaContent != '' and params.shangjiaContent != null and params.shangjiaContent != 'null' ">
                and shangjia.shangjia_content like CONCAT('%',#{params.shangjiaContent},'%')
            </if>
            <if test="params.shangjiaDeleteStart != null  and params.shangjiaDeleteStart != '' ">
                <![CDATA[  and shangjia.shangjia_delete >= #{params.shangjiaDeleteStart}   ]]>
            </if>
            <if test="params.shangjiaDeleteEnd != null  and params.shangjiaDeleteEnd != '' ">
                <![CDATA[  and shangjia.shangjia_delete <= #{params.shangjiaDeleteEnd}   ]]>
            </if>
            <if test="params.shangjiaDelete != null  and params.shangjiaDelete != '' ">
                and shangjia.shangjia_delete = #{params.shangjiaDelete}
            </if>
        </where>

        order by a.${params.orderBy} desc 
    </select>

</mapper>