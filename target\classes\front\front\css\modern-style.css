/* 优雅简约样式库 */

/* 全局样式重置和基础设置 */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    margin: 0;
    padding: 0;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 优雅的容器样式 */
.elegant-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
}

/* 优雅卡片样式 */
.elegant-card {
    background: var(--backgroundColor);
    border-radius: 12px;
    border: 1px solid var(--borderColor);
    box-shadow: 0 4px 16px var(--shadowColor);
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
}

.elegant-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0,0,0,0.12);
    border-color: var(--publicSubColor);
}

.elegant-card-header {
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid var(--borderColor);
    margin-bottom: 24px;
}

.elegant-card-body {
    padding: 0 24px 24px 24px;
}

/* 优雅按钮样式 */
.elegant-btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: var(--publicSubColor);
    color: #fff;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    line-height: 1;
}

.elegant-btn:hover {
    background: #2f855a;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
}

.elegant-btn:active {
    transform: translateY(0);
}

.elegant-btn-outline {
    background: transparent;
    color: var(--publicSubColor);
    border: 1px solid var(--publicSubColor);
}

.elegant-btn-outline:hover {
    background: var(--publicSubColor);
    color: #fff;
}

.elegant-btn-ghost {
    background: transparent;
    color: var(--textSecondary);
    border: 1px solid var(--borderColor);
}

.elegant-btn-ghost:hover {
    color: var(--publicSubColor);
    border-color: var(--publicSubColor);
}

/* 优雅标题样式 */
.elegant-title {
    text-align: center;
    margin: 48px 0 32px 0;
}

.elegant-title h2 {
    font-size: 28px;
    font-weight: 600;
    color: var(--textColor);
    margin: 0 0 8px 0;
    line-height: 1.3;
}

.elegant-title .subtitle {
    font-size: 16px;
    color: var(--textSecondary);
    margin: 0;
    font-weight: 400;
}

.elegant-title.with-icon h2 {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.elegant-title.with-icon i {
    color: var(--publicSubColor);
    font-size: 24px;
}

/* 现代化网格布局 */
.modern-grid {
    display: grid;
    gap: 30px;
    padding: 20px;
}

.modern-grid-2 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.modern-grid-3 {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.modern-grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

/* 现代化图片样式 */
.modern-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.modern-image:hover {
    transform: scale(1.05);
}

/* 现代化加载动画 */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 3px solid rgba(52, 152, 219, 0.3);
    border-radius: 50%;
    border-top-color: var(--publicSubColor);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 现代化渐变背景 */
.gradient-bg {
    background: linear-gradient(135deg,
        var(--publicMainColor) 0%,
        var(--publicSubColor) 50%,
        #5dade2 100%);
}

.gradient-bg-light {
    background: linear-gradient(135deg,
        rgba(44, 62, 80, 0.05) 0%,
        rgba(52, 152, 219, 0.05) 100%);
}

/* 现代化阴影效果 */
.shadow-sm {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.shadow-md {
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.shadow-lg {
    box-shadow: 0 8px 30px rgba(0,0,0,0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .modern-grid {
        grid-template-columns: 1fr;
        gap: 20px;
        padding: 15px;
    }

    .modern-title h2 {
        font-size: 24px;
    }

    #header .navs {
        padding: 0 15px;
    }

    #header .navs li {
        padding: 0 12px;
        margin: 0 4px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .modern-title h2 {
        font-size: 20px;
    }

    .modern-btn {
        padding: 10px 20px;
        font-size: 12px;
    }
}

/* 滚动条美化 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--publicMainColor), var(--publicSubColor));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--publicMainColor);
}

/* 优雅表单样式 */
.elegant-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--borderColor);
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.2s ease;
    background: var(--backgroundColor);
    color: var(--textColor);
}

.elegant-input:focus {
    outline: none;
    border-color: var(--publicSubColor);
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1);
}

.elegant-input::placeholder {
    color: var(--textSecondary);
}

/* 输入框焦点状态优化 */
input:focus {
    outline: none !important;
    border-color: var(--publicSubColor) !important;
    box-shadow: 0 0 0 3px rgba(56, 161, 105, 0.1) !important;
}

/* 现代化标签样式 */
.modern-tag {
    display: inline-block;
    padding: 4px 12px;
    background: var(--publicSubColor);
    color: #fff;
    border-radius: 15px;
    font-size: 12px;
    font-weight: 500;
    margin: 2px;
}

.modern-tag.secondary {
    background: #95a5a6;
}

.modern-tag.success {
    background: #27ae60;
}

.modern-tag.warning {
    background: #f39c12;
}

.modern-tag.danger {
    background: var(--accentColor);
}
