<!-- 首页 -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <title>首页</title>
    <link rel="stylesheet" href="./layui/css/layui.css">
    <link rel="stylesheet" href="./xznstatic/css/common.css"/>
    <link rel="stylesheet" href="css/theme.css"/>
    <link rel="stylesheet" href="css/modern-style.css"/>
    <!-- 引入优雅图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<style type="text/css">
    html, body {
        height: 100%;
    }
</style>
<body>


<div id="header">
    <div v-if='true' class="nav-top">
        <img v-if='false' class="nav-top-img" src='https://www.baidu.com/img/flexible/logo/pc/<EMAIL>'>
        <div v-if="true" class="nav-top-title">{{projectName}}</div>
        <div class="nav-top-tel"></div>
    </div>
    <div class="navs">
        <!-- <div class="logo" style="font-size: 20px;top: 32px;color: #fff;font-weight: bold;margin-left: -200px;width: 240px;" v-text="projectName"></div> -->
        <div class="title" v-if="false" v-text="projectName"></div>
        <div class="list">
            <ul>
                <li class='current'><a href="javascript:navPage('./pages/home/<USER>')" class="menumain"><i data-lucide="home"></i>首页</a></li>
                <li v-for="(item,index) in indexNav" v-bind:key="index"><a :href="'javascript:navPage(\''+item.url+'\')'" class="menumain" style="cursor: pointer;"><i data-lucide="box"></i>{{item.name}}</a></li>
                <li><a href="javascript:centerPage();" class="menumain"><i data-lucide="user"></i>个人中心</a></li>
                <li><a :href="adminurl" target="_blank" class="menumain" style="cursor: pointer;"><i data-lucide="settings"></i>后台管理</a></li>
                <li><a href="javascript:navPage('./pages/cart/list.html')" class="menumain"><i data-lucide="shopping-cart"></i>购物车</a></li>
            </ul>
        </div>
    </div>
</div>



<iframe src="./pages/home/<USER>" id="iframe" frameborder="0" scrolling="no" width="100%" onload="changeFrameHeight"></iframe>

<div id="tabbar" v-if="true" class="elegant-footer" style="background: var(--secondaryBg); border-top: 1px solid var(--borderColor); padding: 32px 0; margin-top: 48px;">
    <div class="elegant-container">
        <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 24px;">
            <div style="flex: 1; min-width: 200px;">
                <div style="color: var(--textColor); font-size: 16px; font-weight: 600; margin-bottom: 8px;">
                    <i data-lucide="building" style="width: 16px; height: 16px; margin-right: 8px; vertical-align: text-bottom;"></i>
                    <span class="company"></span>
                </div>
                <div style="color: var(--textSecondary); font-size: 14px; line-height: 1.5;">
                    <span class="record"></span>
                </div>
            </div>
            <div style="flex: 1; min-width: 200px; text-align: center;">
                <div style="background: var(--backgroundColor); border: 1px solid var(--borderColor); padding: 12px 20px; border-radius: 8px; color: var(--textSecondary); font-size: 14px;">
                    <i data-lucide="info" style="width: 14px; height: 14px; margin-right: 6px; vertical-align: text-bottom;"></i>
                    <span class="desc"></span>
                </div>
            </div>
        </div>
        <div style="text-align: center; margin-top: 24px; padding-top: 24px; border-top: 1px solid var(--borderColor);">
            <div style="color: var(--textSecondary); font-size: 13px;">
                © 2024 校园商铺系统. All rights reserved.
            </div>
        </div>
    </div>
</div>

<script src="./xznstatic/js/jquery-1.11.3.min.js"></script>
<script src="./layui/layui.js"></script>
<script src="./js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="xznstatic/css/element.min.css">
<script src="./js/config.js"></script>

<script>
    var vue1 = new Vue({el: '#tabbar'})

    var vue = new Vue({
        el: '#header',
        data: {
            iconArr: ['layui-icon-gift','layui-icon-email','layui-icon-logout','layui-icon-transfer','layui-icon-slider','layui-icon-print','layui-icon-cols','layui-icon-snowflake','layui-icon-note','layui-icon-flag','layui-icon-theme','layui-icon-website','layui-icon-console','layui-icon-face-surprised','layui-icon-template-1','layui-icon-app','layui-icon-read','layui-icon-component','layui-icon-file-b','layui-icon-unlink','layui-icon-tabs','layui-icon-form','layui-icon-chat'],
            modernIconArr: ['fa-box','fa-store','fa-users','fa-chart-line','fa-tags','fa-bell','fa-comments','fa-star','fa-heart','fa-bookmark','fa-search','fa-filter','fa-download','fa-upload','fa-share','fa-print','fa-edit','fa-trash','fa-plus','fa-minus','fa-check','fa-times','fa-info'],
            indexNav: indexNav,
            adminurl: adminurl,
            projectName: projectName,
        },
        mounted: function() {
            this.bindClickOnLi();
            // 初始化Lucide图标
            this.$nextTick(() => {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
            });
        },
        created() {
            this.iconArr.sort(()=>{
                return (0.5-Math.random())
        })
        },
        methods: {
            jump(url) {
                jump(url)
            },
            bindClickOnLi() {
                let list = document.getElementsByTagName("li");
                for(var i = 0;i<list.length;i++){
                    list[i].onclick = function(){
                        $(this).addClass("current").siblings().removeClass("current");
                    }
                }
            }
        }
    });

    layui.use(['element','layer'], function() {
        var element = layui.element;
        var layer = layui.layer;
    });

    function chatTap(){
        var userTable = localStorage.getItem('userTable');
        if (userTable) {
            layui.layer.open({
                type: 2,
                title: '客服',
                area: ['600px', '600px'],
                content: './pages/chat/list.html'
            });
        } else {
            window.location.href = './pages/login/login.html'
        }
    }

    // 导航栏跳转
    function navPage(url) {
        localStorage.setItem('iframeUrl', url);
        document.getElementById('iframe').src = url;
    }

    // 跳转到个人中心也
    function centerPage() {
        var userTable = localStorage.getItem('userTable');
        if (userTable) {
            localStorage.setItem('iframeUrl', './pages/' + userTable + '/center.html');
            document.getElementById('iframe').src = './pages/' + userTable + '/center.html';
        } else {
            window.location.href = './pages/login/login.html'
        }
    }

    var iframeUrl = localStorage.getItem('iframeUrl');
    document.getElementById('iframe').src = iframeUrl  || './pages/home/<USER>';

    // var i = 0;
    setInterval(function(){
        // i++;
        // if(i<50) changeFrameHeight();
        changeFrameHeight();
    },200)

    function changeFrameHeight() {
        var iframe = document.getElementById('iframe');
        // iframe.height = 'auto';
        if (iframe) {
            var iframeWin = iframe.contentWindow || iframe.contentDocument.parentWindow;
            if (iframeWin.document.body) {
                iframe.height = iframeWin.document.body.scrollHeight;
            }
        }
    };

    //  窗口变化时候iframe自适应
    // function changeFrameHeight() {
    // var header = document.getElementById('header').scrollHeight;
    //     let isshow = true
    //     var tabbar = 0
    //     if(isshow) {
    //       tabbar = document.getElementById('tabbar').scrollHeight
    //     }
    // var ifm = document.getElementById("iframe");
    // ifm.height = document.documentElement.clientHeight - header - tabbar;
    // ifm.width = document.documentElement.clientWidth;
    // }

    // reasize 事件 窗口大小变化后执行的方法
    window.onresize = function() {
        changeFrameHeight();
    }
</script>
</body>
</html>
