/* 已售罄标识样式 */

/* 商品列表页面的已售罄标识 */
.sold-out-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    font-size: 18px;
    font-weight: bold;
    z-index: 10;
    pointer-events: none;
    text-align: center;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

/* 商品详情页面的已售罄标识 */
.sold-out-overlay-detail {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.8);
    color: white;
    padding: 15px 30px;
    border-radius: 8px;
    font-size: 24px;
    font-weight: bold;
    z-index: 1000;
    pointer-events: none;
    text-align: center;
    white-space: nowrap;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

/* 已售罄按钮样式 */
.sold-out-button {
    background-color: #808080 !important;
    color: #ffffff !important;
    cursor: not-allowed !important;
    opacity: 0.7;
    transition: all 0.3s ease;
}

.sold-out-button:hover {
    background-color: #808080 !important;
    color: #ffffff !important;
    opacity: 0.7;
}

/* 商品容器相对定位 */
.product-container {
    position: relative;
    overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sold-out-overlay {
        font-size: 16px;
        padding: 8px 16px;
    }
    
    .sold-out-overlay-detail {
        font-size: 20px;
        padding: 12px 24px;
    }
}

@media (max-width: 480px) {
    .sold-out-overlay {
        font-size: 14px;
        padding: 6px 12px;
    }
    
    .sold-out-overlay-detail {
        font-size: 18px;
        padding: 10px 20px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.sold-out-overlay,
.sold-out-overlay-detail {
    animation: fadeIn 0.3s ease-out;
}

/* 商品图片灰度效果（可选） */
.product-image-sold-out {
    filter: grayscale(50%);
    opacity: 0.8;
    transition: all 0.3s ease;
}

/* 悬停效果 */
.gallery-demo:hover .sold-out-overlay {
    background: rgba(0, 0, 0, 0.8);
    transform: translate(-50%, -50%) scale(1.05);
}

.gallery-demo:hover .sold-out-overlay-detail {
    background: rgba(255, 0, 0, 0.9);
    transform: translate(-50%, -50%) scale(1.02);
}
