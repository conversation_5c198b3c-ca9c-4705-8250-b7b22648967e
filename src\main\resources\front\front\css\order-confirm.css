/* 订单二级确认弹窗样式 */

/* 确认弹窗容器 */
.order-confirm-dialog {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
}

/* 标题样式 */
.order-confirm-title {
    margin: 0 0 20px 0;
    color: #333;
    text-align: center;
    border-bottom: 2px solid #ff4757;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: bold;
}

.order-confirm-title .layui-icon {
    color: #ff4757;
    margin-right: 8px;
}

/* 信息区块 */
.info-section {
    margin-bottom: 20px;
}

.info-section h4 {
    color: #333;
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: bold;
}

.info-section h4 .layui-icon {
    color: #ff4757;
    margin-right: 8px;
}

/* 信息内容框 */
.info-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #ff4757;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 地址信息 */
.address-info .recipient-name {
    font-weight: bold;
    color: #333;
    font-size: 16px;
}

.address-info .recipient-phone {
    color: #666;
    margin-top: 5px;
}

.address-info .recipient-address {
    color: #666;
    margin-top: 5px;
    line-height: 1.4;
}

/* 商品列表 */
.product-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.product-item:hover {
    background-color: rgba(255, 71, 87, 0.05);
}

.product-item:last-child {
    border-bottom: none;
}

.product-left {
    display: flex;
    align-items: center;
}

.product-image {
    width: 50px;
    height: 50px;
    object-fit: cover;
    margin-right: 10px;
    border-radius: 4px;
    border: 1px solid #eee;
}

.product-info .product-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 4px;
}

.product-info .product-price {
    color: #666;
    font-size: 12px;
}

.product-right {
    text-align: right;
}

.product-right .product-quantity {
    color: #333;
    margin-bottom: 4px;
}

.product-right .product-total {
    color: #ff4757;
    font-weight: bold;
}

/* 支付信息 */
.payment-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 4px 0;
}

.payment-row.total {
    padding-top: 8px;
    border-top: 1px solid #ddd;
    margin-top: 8px;
}

.payment-label {
    color: #666;
}

.payment-value {
    color: #333;
    font-weight: bold;
}

.payment-value.highlight {
    color: #ff4757;
    font-size: 18px;
}

/* 提示信息 */
.confirm-tips {
    text-align: center;
    color: #666;
    font-size: 14px;
    margin-top: 20px;
    padding: 15px;
    background: #fff9e6;
    border-radius: 6px;
    border: 1px solid #ffd700;
}

.confirm-tips .layui-icon {
    color: #ffa502;
    margin-right: 5px;
}

/* 成功提示样式 */
.success-dialog {
    text-align: center;
    padding: 40px 20px;
}

.success-icon {
    font-size: 60px;
    color: #52c41a;
    margin-bottom: 20px;
    animation: successPulse 1.5s ease-in-out;
}

.success-title {
    color: #333;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: bold;
}

.success-message {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
}

/* 失败提示样式 */
.error-dialog {
    text-align: center;
    padding: 40px 20px;
}

.error-icon {
    font-size: 60px;
    color: #ff4757;
    margin-bottom: 20px;
    animation: errorShake 0.5s ease-in-out;
}

.error-title {
    color: #333;
    margin-bottom: 15px;
    font-size: 20px;
    font-weight: bold;
}

.error-message {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
}

/* 动画效果 */
@keyframes successPulse {
    0% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes errorShake {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-5px);
    }
    75% {
        transform: translateX(5px);
    }
}

/* 按钮样式增强 */
.layui-layer-btn .layui-layer-btn0 {
    background-color: #ff4757 !important;
    border-color: #ff4757 !important;
    color: white !important;
    font-weight: bold;
    transition: all 0.3s ease;
}

.layui-layer-btn .layui-layer-btn0:hover {
    background-color: #ff3838 !important;
    border-color: #ff3838 !important;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(255, 71, 87, 0.3);
}

.layui-layer-btn .layui-layer-btn1 {
    background-color: #f8f9fa !important;
    border-color: #dee2e6 !important;
    color: #666 !important;
    transition: all 0.3s ease;
}

.layui-layer-btn .layui-layer-btn1:hover {
    background-color: #e9ecef !important;
    border-color: #adb5bd !important;
    color: #495057 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .order-confirm-dialog {
        padding: 15px;
    }
    
    .product-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px 0;
    }
    
    .product-right {
        margin-top: 8px;
        text-align: left;
    }
    
    .payment-row {
        font-size: 14px;
    }
    
    .confirm-tips {
        font-size: 13px;
        padding: 12px;
    }
}

/* 滚动条样式 */
.order-confirm-dialog::-webkit-scrollbar {
    width: 6px;
}

.order-confirm-dialog::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.order-confirm-dialog::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.order-confirm-dialog::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
