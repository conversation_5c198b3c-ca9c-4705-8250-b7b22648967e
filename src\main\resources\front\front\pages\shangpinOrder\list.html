<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="utf-8">
    <title>商品订单</title>
    <meta name="keywords" content=""/>
    <meta name="description" content=""/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../xznstatic/css/common.css"/>
    <link rel="stylesheet" href="../../xznstatic/css/style.css"/>
    <script type="text/javascript" src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
    <script type="text/javascript" src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
    <link rel="stylesheet" href="../../xznstatic/css/bootstrap.min.css" />

    <link rel="stylesheet" href="../../css/theme.css"/>
</head>
<style>
    html::after {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        content: '';
        display: block;
        background-attachment: fixed;
        background-size: cover;
        background-position: center;
    }

    /*轮播图相关 start*/
    #swiper {
        overflow: hidden;
    }

    #swiper .layui-carousel-ind li {
        width: 20px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0, 0, 0, .3);
        border-radius: 6px;
        background-color: #f7f7f7;
        box-shadow: 0 0 6px rgba(255, 0, 0, .8);
    }

    #swiper .layui-carousel-ind li.layui-this {
        width: 30px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0, 0, 0, .3);
        border-radius: 6px;
    }
    /*轮播图相关 end*/

    /*列表*/
    .recommend {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }

    .recommend .box {
        width: 1002px;
        margin: 0 auto;
    }

    .recommend .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
    }

    .recommend .box .title span {
        padding: 0 10px;
        font-size: 16px;
        line-height: 1.4;
    }

    .recommend .box .filter {
        padding: 0 10px;
        display: flex;
        align-items: center;
        box-sizing: border-box;
        width: 100%;
        flex-wrap: wrap;
    }

    .recommend .box .filter .item-list {
        display: flex;
        align-items: center;
    }

    .recommend .box .filter .item-list .lable {
        font-size: 14px;
        color: #333;
        box-sizing: border-box;
    }

    .recommend .box .filter .item-list input {
        padding: 0 10px;
        box-sizing: border-box;
        outline: none;
    }

    .recommend .box .filter button {
        display: flex;
        padding: 0 10px;
        box-sizing: border-box;
        align-items: center;
        justify-content: center;
        outline: none;
    }

    .recommend .box .filter button i {
        margin-right: 4px;
    }

    .recommend .box .list {
        display: flex;
        flex-wrap: wrap;
    }

    .recommend .box .list .list-item {
        flex: 0 0 25%;
        padding: 0 5px;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item .list-item-body {
        cursor: pointer;
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 5px;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item-body img {
        width: 100%;
        height: 100px;
        display: block;
        margin: 0 auto;
    }

    .recommend .box .list .list-item-body .info {
        display: flex;
        flex-wrap: wrap;
    }

    .recommend .box .list .list-item-body .info .price {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        text-align: center;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item-body .info .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        text-align: center;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item3 {
        flex: 0 0 33.33%;
    }

    .recommend .box .list .list-item5 {
        flex: 0 0 25%;
    }

    .recommend .box .news {
        display: flex;
        flex-wrap: wrap;
        padding: 0;
        width: 100%;
    }

    .recommend .box .news .list-item {
        flex: 0 0 50%;
        padding: 0 10px;
        box-sizing: border-box;
    }

    .recommend .box .news .list-item .list-item-body {
        cursor: pointer;
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 10px;
        box-sizing: border-box;
        display: flex;
    }

    .recommend .box .news .list-item .list-item-body img {
        width: 120px;
        height: 100%;
        display: block;
        margin: 0 auto;
    }

    .recommend .box .news .list-item .list-item-body .item-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding-left: 10px;
        box-sizing: border-box;
    }

    .recommend .box .news .list-item .list-item-body .item-info .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .recommend .box .news .list-item .list-item-body .item-info .time {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
        box-sizing: border-box;
    }

    .recommend .box .news .list-item1 {
        flex: 0 0 100%;
    }

    .recommend .box .news .list-item3 {
        flex: 0 0 33.33%;
    }

    .index-pv1 .animation-box:hover {
        transform: perspective(1000px) translate3d(0px, -10px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
        transition: all 0.3s;
    }


    .layui-laypage .layui-laypage-count {
        padding: 0 10px;
    }

    .layui-laypage .layui-laypage-skip {
        padding-left: 10px;
    }
    /*标题*/
    .index-title {
        text-align: center;
        box-sizing: border-box;
        width: 980px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }
    .index-title span {
        padding: 0 10px;
        line-height: 1.4;
    }
    /* 订单列表位置 start */


    .center-container {
        width: 980px;
        margin: 0 auto;
        margin-top: 20px;
        text-align: center;
        display: flex;
        margin-bottom: 20px;
    }

    .center-container .left-container {
        border: 2px dotted #EEEEEE;
        background: #FFFFFF;
        width: 200px;
        padding-top: 20px;
        height: 600px;
    }

    .center-container .right-container {
        flex: 1;
        border: 2px dotted #EEEEEE;
        background: #FFFFFF;
        text-align: left;
        padding: 20px;
        padding-top: 40px;
    }

    .center-container .layui-nav-tree {
        width: 100%;
    }
    .center-container .layui-nav {
        position: inherit;
    }
    .center-container .layui-nav-tree .layui-nav-item {
        height: 44px;
        line-height: 44px;
        font-size: 16px;
        color: rgba(17, 17, 17, 1);
        border-width: 0px 0px 1px 0px;
        border-style: solid;
        border-radius: 0;
        background-color: #fff;
        text-align: center;
    }
    .center-container .layui-nav-tree .layui-nav-item.layui-this {
        font-size: 16px;
        color: rgba(17, 17, 17, 1);
        border-width: 0;
        border-style: solid;
        border-radius: 0;
    }
    .center-container .layui-nav-tree .layui-nav-item:hover {
        font-size: 14px;
        color: #fff;
        border-width: 0;
        border-style: solid;
        border-radius: 0;
    }
    .center-container .layui-nav-tree .layui-nav-item a {
        line-height: inherit;
        height: auto;
        background-color: inherit;
        color: inherit;
        text-decoration: none;
    }
    /* 订单列表位置 end */

    /*表格 end*/
</style>
<body>
<div id="app">
    <div class="banner">
        <div class="layui-carousel" id="swiper"
             :style='{"boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"0 auto","borderColor":"rgba(0,0,0,.3)","borderRadius":"0px","borderWidth":"0","width":"100%","borderStyle":"solid"}'>
            <div carousel-item>
                <div v-for="(item,index) in swiperList" :key="index">
                    <img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img"/>
                </div>
            </div>
        </div>
    </div>
    <div class="index-title sub_backgroundColor" :style='{"padding":"10px","boxShadow":"0 0 2px rgba(160, 67, 26, 1)","margin":"10px auto","borderColor":"rgba(0,0,0,.3)","color":"rgba(255, 255, 255, 1)","borderRadius":"4px","borderWidth":"0","fontSize":"20px","borderStyle":"solid","height":"auto"}'>
        <span>USER / ORDER</span><span>我的商品订单</span>
    </div>
    <!-- 标题 -->
    <el-dialog title="评论" :visible.sync="shangpinCommentbackModal" :modal-append-to-body="false">
        <el-form>
            <el-form-item label="评论信息">
                <el-input type="textarea" v-model="shangpinCommentbackContent"></el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <el-button @click="shangpinCommentbackModal = false">取 消</el-button>
            <el-button type="primary" @click="submitShangpinCommentback()">确 定</el-button>
        </div>
    </el-dialog>



    <div class="center-container" style="width: 100%;">
        <!-- 个人中心菜单 -->
        <div class="right-container" style="padding-top: 0;">
            <div class="layui-tab layui-tab-brief" lay-filter="docDemoTabBrief">
                <ul class="layui-tab-title">
                    <li class="layui-this" @click="pageList(0)">全部商品订单</li>
                    <li v-for="item in shangpinOrderTypesList" :value="item.codeIndex"  @click="pageList(item.codeIndex)">{{item.indexName}}</li>
                </ul>
                <div class="layui-tab-content">
                    <table class="layui-table" lay-skin="nob">
                        <thead>
                        <tr>
                            <th>商品名称</th>
                            <th>商品照片</th>
                            <th>商品类型</th>
                            <th>商品库存</th>
                            <th>商品原价</th>
                            <th>现价</th>
                            <th>点击次数</th>
                            <th>是否上架</th>
                            <th>订单号</th>
                            <th>购买数量</th>
                            <th>实付价格</th>
                            <th>订单类型</th>
                            <th>支付类型</th>
                            <th>订单创建时间</th>
                            <td>操作</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="(item,index) in dataList" v-bind:key="index">
                            <td style="width: 60px;">{{item.shangpinName}}</td>
                            <td style="width: 250px;">
                                <img :src="item.shangpinPhoto" style="width: 100px;height: 100px;object-fit: cover;">
                            </td>
                            <td style="width: 80px;">{{item.shangpinValue}}</td>
                            <td style="width: 60px;">{{item.shangpinKucunNumber}}</td>
                            <td style="width: 100px;color: red">{{item.shangpinOldMoney}}</td>
                            <td style="width: 100px;color: red">{{item.shangpinNewMoney}}</td>
                            <td style="width: 60px;">{{item.shangpinClicknum}}</td>
                            <td style="width: 80px;">{{item.shangxiaValue}}</td>
                            <td style="width: 150px;">{{item.shangpinOrderUuidNumber}}</td>
                            <td style="width: 150px;">{{item.buyNumber}}</td>
                            <td style="width: 150px;">{{item.shangpinOrderTruePrice}}</td>
                            <td style="width: 80px;">{{item.shangpinOrderValue}}</td>
                            <td style="width: 80px;">{{item.shangpinOrderPaymentValue}}</td>
                            <td style="width: 150px;">{{item.insertTime}}</td>
                            <td style="width: 100px;">




                                <button v-if="item.shangpinOrderTypes==3" @click="refund(item.id)" type="button" class="layui-btn layui-btn-sm layui-btn-radius layui-btn-warm">
                                    <i class="layui-icon">&#xe65e;</i> 退款
                                </button>


                                <button v-if="item.shangpinOrderTypes==4" @click="receiving(item.id)" type="button" class="layui-btn layui-btn-sm layui-btn-radius layui-btn-warm">
                                    <i class="layui-icon">&#xe65e;</i> 收货
                                </button>




                                <button v-if="item.shangpinOrderTypes==5" @click="commentback(item.id)" type="button" class="layui-btn layui-btn-sm layui-btn-radius btn-theme">
                                    <i class="layui-icon">&#xe65e;</i> 评价
                                </button>

                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <div class="pager" id="pager" :style="{textAlign:2==1?'left':2==2?'center':'right'}"></div>
                </div>
            </div>
        </div>
    </div>
</div></div>

<script src="../../xznstatic/js/bootstrap.min.js" type="text/javascript" charset="utf-8"></script>
<script src="../../layui/layui.js"></script>
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">
<script src="../../js/config.js"></script>
<script src="../../modules/config.js"></script>
<script src="../../js/utils.js"></script>

<script type="text/javascript">
    var vue = new Vue({
        el: '#app',
        data: {
            swiperList: [],
            shangpinOrderTypesList: [],
            shangpinOrderPaymentTypesList: [],

            //查询条件
            searchForm: {
                page: 1
                ,limit: 8
                ,shangpinOrderUuidNumber: ""
            },

            //订单评论模态框
            shangpinCommentbackContent: null,
            shangpinCommentbackId: null,
            shangpinCommentbackModal: false,
            shangpinCommentbackPingfenNumber:0,

            dataList: [],
        },
        filters: {
            subString: function(val) {
                if (val) {
                    val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
                    if (val.length > 60) {
                        val = val.substring(0, 60);
                        val+='...';
                    }
                    return val;
                }
                return '';
            }
        },
        computed: {
        },
        methods: {
            isAuth(tablename, button) {
                return isFrontAuth(tablename, button);
            }
            ,jump(url) {
                jump(url);
            }
            ,jumpCheck(url,check1,check2) {
                if(check1 == "2" || check1 == 2){//级联表的逻辑删除字段[1:未删除 2:已删除]
                    layui.layer.msg("已经被删除", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                if(check2 == "2"  || check2 == 2){//是否下架[1:上架 2:下架]
                    layui.layer.msg("已经下架", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                this.jump(url);
            }
            //列表查询
            ,pageList(shangpinOrderTypes) {
                this.shangpinOrderTypes = shangpinOrderTypes;
                // 获取列表数据
                layui.http.request('shangpinOrder/page', 'get', {
                    page: 1,
                    limit: 8,
                    shangpinOrderTypes: (vue.shangpinOrderTypes == 0)?null:vue.shangpinOrderTypes
                }, function(res) {
                    vue.dataList = res.data.list;
                    // 分页
                    layui.laypage.render({
                        elem: 'pager',
                        count: res.data.total,
                        limit: 5,
                        jump: function(obj, first) {
                            //首次不执行
                            if (!first) {
                                layui.http.request('shangpinOrder/page', 'get', {
                                    page: obj.curr,
                                    limit: obj.limit,
                                    shangpinOrderTypes: (this.shangpinOrderTypes == 0)?null:this.shangpinOrderTypes
                                }, function(res) {
                                    this.dataList = res.data.list
                                });
                            }
                        }
                    });
                });
            }
            // 支付
            ,pay(id) {
                var mymessage = confirm("确定要支付吗？");
                if(!mymessage){
                    alert("已取消操作");
                    return false;
                }
                // 获取商品详情信息
                layui.http.request(`shangpinOrder/pay?id=`+id, 'GET',{}, (res) => {
                    if(res.code==0){
                        layer.msg('支付成功', {
                            time: 2000,
                            icon: 6
                        });
                        window.location.reload();
                    }else{
                        layer.msg(res.msg, {
                            time: 2000,
                            icon: 2
                        });
                    }
                });
            }
            // 退款
            ,refund(id) {
                var mymessage = confirm("确定要退款吗？");
                if(!mymessage){
                    alert("已取消操作");
                    return false;
                }
                // 获取商品详情信息
                layui.http.request(`shangpinOrder/refund?id=`+id, 'get', {}, (res) => {
                    if(res.code==0){
                        layer.msg('退款成功', {
                            time: 2000,
                            icon: 6
                        });
                        window.location.reload();
                    }else{
                        layer.msg(res.msg, {
                            time: 2000,
                            icon: 2
                        });
                    }
                });
            }
            // 收货
            ,receiving(id) {
                var mymessage = confirm("确定要收货吗？");
                if(!mymessage){
                    alert("已取消操作");
                    return false;
                }
                // 获取商品详情信息
                layui.http.request(`shangpinOrder/receiving?id=`+id, 'get', {}, (res) => {
                    if(res.code==0){
                        layer.msg('成功收货', {
                            time: 2000,
                            icon: 6
                        });
                        window.location.reload();
                    }else{
                        layer.msg(res.msg, {
                            time: 2000,
                            icon: 2
                        });
                    }
                });
            }
            // 评价
            ,commentback(id) {
                this.shangpinCommentbackId = null//制空订单id
                this.shangpinCommentbackContent = null//制空评论内容
                this.shangpinCommentbackId = id//设置订单id
                this.shangpinCommentbackModal = true//打开模态框
            }
            ,submitShangpinCommentback(){
                 if(this.shangpinCommentbackContent == null || this.shangpinCommentbackContent == "" || this.shangpinCommentbackContent == "null"){
                    layer.msg("评论内容不能为空", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                let _this = this
                layui.http.request("shangpinOrder/commentback?id="+this.shangpinCommentbackId+"&commentbackText="+this.shangpinCommentbackContent + "&shangpinCommentbackPingfenNumber="+this.shangpinCommentbackPingfenNumber, 'get', {}, function (res) {
                    if(res.code==0){
                        layer.msg('评价成功', {
                            time: 2000,
                            icon: 6
                        });
                        window.location.reload();
                    }else{
                        layer.msg(res.msg, {
                            time: 2000,
                            icon: 2
                        });
                    }
                    _this.shangpinCommentbackModal = false
                });
            }        }
    });

    layui.use(['layer', 'element', 'carousel', 'laypage', 'http', 'jquery', 'laydate', 'tinymce'], function() {
        var layer = layui.layer;
        var element = layui.element;
        var carousel = layui.carousel;
        var laypage = layui.laypage;
        var http = layui.http;
        var laydate = layui.laydate;
        var tinymce = layui.tinymce;
        window.jQuery = window.$ = jquery = layui.jquery;

        // var id = http.getParam('id');

        // 获取轮播图 数据
        http.request('config/list', 'get', {
            page: 1,
            limit: 5
        }, function (res) {
            if (res.data.list.length > 0) {
                let swiperList = [];
                res.data.list.forEach(element => {
                    if(element.value != null){
                        swiperList.push({
                            img: element.value
                        });
                    }
                });
                vue.swiperList = swiperList;

                vue.$nextTick(() => {
                    carousel.render({
                        elem: '#swiper',
                        width: '100%',
                        height: '450px',
                        arrow: 'hover',
                        anim: 'default',
                        autoplay: 'true',
                        interval: '3000',
                        indicator: 'inside'
                    });
                });
            }
        });

        //订单类型的动态搜素
        $(document).on("click", ".thisTableType-search", function (e) {
            vue.searchForm.shangpinOrderTypes = $(this).attr('index');
            pageList();
        });


           //当前表的 订单类型 字段 字典表查询方法
           function shangpinOrderTypesSelect() {
               http.request("dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangpin_order_types", 'get', {}, function (res) {
                   if(res.code == 0){
                       vue.shangpinOrderTypesList = res.data.list;
                   }
               });
           }
           //当前表的 支付类型 字段 字典表查询方法
           function shangpinOrderPaymentTypesSelect() {
               http.request("dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangpin_order_payment_types", 'get', {}, function (res) {
                   if(res.code == 0){
                       vue.shangpinOrderPaymentTypesList = res.data.list;
                   }
               });
           }


        // 获取列表数据
        http.request('shangpinOrder/page', 'get',{
            page: 1,
            limit:  vue.searchForm.limit,
            shangpinOrderTypes: (vue.shangpinOrderTypes == 0)?null:vue.shangpinOrderTypes
        }, function(res) {
            vue.dataList = res.data.list;
            // 分页
            laypage.render({
                elem: 'pager',
                count: res.data.total,
                limit: vue.searchForm.limit,
                jump: function(obj, first) {
                    //首次不执行  !first 是 layui的自带方法
                    if (!first) {
                        http.request('shangpinOrder/page', 'get', {
                            page: obj.curr,
                            limit: obj.limit,
                            shangpinOrderType: (vue.shangpinOrderTypes == 0)?null:vue.shangpinOrderTypes
                        }, function(res) {
                            vue.dataList = res.data.list
                        })
                    }
                }
            });
        });
        // 订单类型的查询
                shangpinOrderTypesSelect();
       function shangpinOrderTypesSelect() {
           http.request("dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangpin_order_types", 'get', {}, function (res) {
               if(res.code == 0){
                   vue.shangpinOrderTypesList = res.data.list;
               }
           });
       }
        // 支付类型的查询
                shangpinOrderPaymentTypesSelect();
       function shangpinOrderPaymentTypesSelect() {
           http.request("dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangpin_order_payment_types", 'get', {}, function (res) {
               if(res.code == 0){
                   vue.shangpinOrderPaymentTypesList = res.data.list;
               }
           });
       }
    });

    window.xznSlide = function () {
        jQuery(".banner").slide({mainCell: ".bd ul", autoPlay: true, interTime: 5000});
        jQuery("#ifocus").slide({
            titCell: "#ifocus_btn li",
            mainCell: "#ifocus_piclist ul",
            effect: "leftLoop",
            delayTime: 200,
            autoPlay: true,
            triggerTime: 0
        });
        jQuery("#ifocus").slide({titCell: "#ifocus_btn li", mainCell: "#ifocus_tx ul", delayTime: 0, autoPlay: true});
        jQuery(".product_list").slide({
            mainCell: ".bd ul",
            autoPage: true,
            effect: "leftLoop",
            autoPlay: true,
            vis: 5,
            trigger: "click",
            interTime: 4000
        });
    };
</script>
</body>
</html>
