<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设计预览 - 校园商铺系统</title>
    <link rel="stylesheet" href="css/theme.css">
    <link rel="stylesheet" href="css/modern-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: var(--secondaryBg);
        }
        .preview-section {
            margin-bottom: 48px;
        }
        .preview-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--textColor);
            margin-bottom: 16px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--publicSubColor);
        }
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 24px;
            margin-top: 24px;
        }
    </style>
</head>
<body>
    <div class="elegant-container">
        <div class="elegant-title">
            <h2><i data-lucide="palette"></i>设计系统预览</h2>
            <div class="subtitle">优雅简约的界面设计展示</div>
        </div>

        <!-- 色彩系统 -->
        <div class="preview-section">
            <div class="preview-title">色彩系统</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 16px;">
                <div class="elegant-card">
                    <div class="elegant-card-body">
                        <div style="width: 100%; height: 60px; background: var(--publicMainColor); border-radius: 8px; margin-bottom: 12px;"></div>
                        <h4 style="margin: 0 0 4px 0; color: var(--textColor);">主色调</h4>
                        <p style="margin: 0; color: var(--textSecondary); font-size: 14px;">#1a365d</p>
                    </div>
                </div>
                <div class="elegant-card">
                    <div class="elegant-card-body">
                        <div style="width: 100%; height: 60px; background: var(--publicSubColor); border-radius: 8px; margin-bottom: 12px;"></div>
                        <h4 style="margin: 0 0 4px 0; color: var(--textColor);">副色调</h4>
                        <p style="margin: 0; color: var(--textSecondary); font-size: 14px;">#38a169</p>
                    </div>
                </div>
                <div class="elegant-card">
                    <div class="elegant-card-body">
                        <div style="width: 100%; height: 60px; background: var(--accentColor); border-radius: 8px; margin-bottom: 12px;"></div>
                        <h4 style="margin: 0 0 4px 0; color: var(--textColor);">辅助色</h4>
                        <p style="margin: 0; color: var(--textSecondary); font-size: 14px;">#ed8936</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 按钮组件 -->
        <div class="preview-section">
            <div class="preview-title">按钮组件</div>
            <div style="display: flex; gap: 16px; flex-wrap: wrap; align-items: center;">
                <button class="elegant-btn">
                    <i data-lucide="check"></i>
                    主要按钮
                </button>
                <button class="elegant-btn-outline">
                    <i data-lucide="edit"></i>
                    次要按钮
                </button>
                <button class="elegant-btn-ghost">
                    <i data-lucide="x"></i>
                    幽灵按钮
                </button>
            </div>
        </div>

        <!-- 卡片组件 -->
        <div class="preview-section">
            <div class="preview-title">卡片组件</div>
            <div class="component-grid">
                <div class="elegant-card">
                    <div class="elegant-card-header">
                        <h3 style="margin: 0; color: var(--textColor);">商品卡片</h3>
                    </div>
                    <div class="elegant-card-body">
                        <div style="width: 100%; height: 120px; background: var(--secondaryBg); border-radius: 8px; margin-bottom: 12px; display: flex; align-items: center; justify-content: center; color: var(--textSecondary);">
                            <i data-lucide="image" style="width: 32px; height: 32px;"></i>
                        </div>
                        <h4 style="margin: 0 0 8px 0; color: var(--textColor);">商品名称</h4>
                        <p style="margin: 0; color: var(--textSecondary); font-size: 14px;">商品描述信息</p>
                    </div>
                </div>
                <div class="elegant-card">
                    <div class="elegant-card-header">
                        <h3 style="margin: 0; color: var(--textColor);">信息卡片</h3>
                    </div>
                    <div class="elegant-card-body">
                        <p style="margin: 0; color: var(--textSecondary); line-height: 1.6;">
                            这是一个信息展示卡片，采用简洁的设计风格，突出内容的可读性和美观性。
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 表单组件 -->
        <div class="preview-section">
            <div class="preview-title">表单组件</div>
            <div class="elegant-card" style="max-width: 400px;">
                <div class="elegant-card-body">
                    <div style="margin-bottom: 20px;">
                        <label style="display: block; margin-bottom: 6px; color: var(--textColor); font-size: 14px; font-weight: 500;">用户名</label>
                        <div style="position: relative;">
                            <i data-lucide="user" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: var(--textSecondary); width: 16px; height: 16px;"></i>
                            <input class="elegant-input" style="padding-left: 40px;" type="text" placeholder="请输入用户名">
                        </div>
                    </div>
                    <div style="margin-bottom: 24px;">
                        <label style="display: block; margin-bottom: 6px; color: var(--textColor); font-size: 14px; font-weight: 500;">密码</label>
                        <div style="position: relative;">
                            <i data-lucide="lock" style="position: absolute; left: 12px; top: 50%; transform: translateY(-50%); color: var(--textSecondary); width: 16px; height: 16px;"></i>
                            <input class="elegant-input" style="padding-left: 40px;" type="password" placeholder="请输入密码">
                        </div>
                    </div>
                    <button class="elegant-btn" style="width: 100%; justify-content: center;">
                        <i data-lucide="log-in"></i>
                        登录
                    </button>
                </div>
            </div>
        </div>

        <!-- 图标系统 -->
        <div class="preview-section">
            <div class="preview-title">图标系统</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 16px; max-width: 600px;">
                <div style="text-align: center; padding: 16px;">
                    <i data-lucide="home" style="width: 24px; height: 24px; color: var(--publicSubColor); margin-bottom: 8px;"></i>
                    <div style="font-size: 12px; color: var(--textSecondary);">首页</div>
                </div>
                <div style="text-align: center; padding: 16px;">
                    <i data-lucide="package" style="width: 24px; height: 24px; color: var(--publicSubColor); margin-bottom: 8px;"></i>
                    <div style="font-size: 12px; color: var(--textSecondary);">商品</div>
                </div>
                <div style="text-align: center; padding: 16px;">
                    <i data-lucide="user" style="width: 24px; height: 24px; color: var(--publicSubColor); margin-bottom: 8px;"></i>
                    <div style="font-size: 12px; color: var(--textSecondary);">用户</div>
                </div>
                <div style="text-align: center; padding: 16px;">
                    <i data-lucide="settings" style="width: 24px; height: 24px; color: var(--publicSubColor); margin-bottom: 8px;"></i>
                    <div style="font-size: 12px; color: var(--textSecondary);">设置</div>
                </div>
                <div style="text-align: center; padding: 16px;">
                    <i data-lucide="shopping-cart" style="width: 24px; height: 24px; color: var(--publicSubColor); margin-bottom: 8px;"></i>
                    <div style="font-size: 12px; color: var(--textSecondary);">购物车</div>
                </div>
                <div style="text-align: center; padding: 16px;">
                    <i data-lucide="bell" style="width: 24px; height: 24px; color: var(--publicSubColor); margin-bottom: 8px;"></i>
                    <div style="font-size: 12px; color: var(--textSecondary);">通知</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 初始化图标
        if (typeof lucide !== 'undefined') {
            lucide.createIcons();
        }
    </script>
</body>
</html>
