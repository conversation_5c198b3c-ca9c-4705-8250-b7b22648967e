<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="utf-8">
    <title>首页</title>
    <meta name="description" content=""/>
    <meta name="keywords" content=""/>
    <meta name="author" content="order by mobanxiu.cn"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link rel="stylesheet" href="../../xznstatic/css/common.css"/>
    <link rel="stylesheet" href="../../xznstatic/css/swiper.min.css"/>
    <script src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
    <script src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>
    <link rel="stylesheet" href="../../css/theme.css"/>
    <link rel="stylesheet" href="../../css/modern-style.css"/>
    <!-- 引入优雅图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/lucide/0.263.1/lucide.min.css">
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
</head>
<style>
    html::after {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        content: '';
        display: block;
        background-attachment: fixed;
        background-size: cover;
        background-position: center;
    }

    /* 现代化轮播图样式 */
    #test1 {
        overflow: hidden;
        border-radius: 20px;
        position: relative;
    }

    /* 自定义指示器样式 */
    #test1 .layui-carousel-ind {
        bottom: 30px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 8px;
    }

    #test1 .layui-carousel-ind li {
        width: 12px;
        height: 12px;
        border-width: 0;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.4);
        transition: all 0.3s ease;
        margin: 0;
        cursor: pointer;
        position: relative;
    }

    #test1 .layui-carousel-ind li.layui-this {
        width: 12px;
        height: 12px;
        background-color: #fff;
        transform: scale(1.2);
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.8);
    }

    #test1 .layui-carousel-ind li:hover {
        background-color: rgba(255, 255, 255, 0.7);
        transform: scale(1.1);
    }

    /* 轮播图内容动画 */
    .carousel-slide {
        animation: slideIn 0.8s ease-out;
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateX(30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* 轮播图标题动画 */
    .carousel-title {
        animation: titleFadeIn 1s ease-out 0.3s both;
    }

    @keyframes titleFadeIn {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 轮播图描述动画 */
    .carousel-description {
        animation: descFadeIn 1s ease-out 0.5s both;
    }

    @keyframes descFadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 轮播图按钮动画 */
    .carousel-actions {
        animation: actionsFadeIn 1s ease-out 0.7s both;
    }

    @keyframes actionsFadeIn {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 按钮悬停效果 */
    .hero-btn.primary:hover {
        background: #2f855a !important;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(56, 161, 105, 0.4);
    }

    .hero-btn.secondary:hover {
        background: rgba(255,255,255,0.3) !important;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255,255,255,0.2);
    }

    /* 导航按钮悬停效果 */
    .carousel-nav-prev:hover,
    .carousel-nav-next:hover {
        background: rgba(255,255,255,0.3) !important;
        transform: translateY(-50%) scale(1.1);
    }

    /* 装饰元素动画 */
    .carousel-decoration > div > div {
        animation: float 3s ease-in-out infinite;
    }

    .carousel-decoration > div > div:nth-child(2) {
        animation-delay: 1.5s;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0);
        }
        50% {
            transform: translateY(-10px);
        }
    }

    /* 信息卡片动画 */
    .info-card {
        animation: cardSlideUp 0.6s ease-out;
        transition: all 0.3s ease;
    }

    .info-card:nth-child(1) {
        animation-delay: 0.1s;
    }

    .info-card:nth-child(2) {
        animation-delay: 0.2s;
    }

    .info-card:nth-child(3) {
        animation-delay: 0.3s;
    }

    @keyframes cardSlideUp {
        from {
            opacity: 0;
            transform: translateY(40px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .info-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    }

    /* 背景装饰动画 */
    .hero-bg-decoration > div {
        animation: bgFloat 6s ease-in-out infinite;
    }

    .hero-bg-decoration > div:nth-child(2) {
        animation-delay: 3s;
    }

    @keyframes bgFloat {
        0%, 100% {
            transform: translate(0, 0) scale(1);
        }
        50% {
            transform: translate(20px, -20px) scale(1.1);
        }
    }

    .recommend {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }

    .recommend .box {
        width: 1014px;
    }

    .recommend .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .recommend .box .title span {
        padding: 0 10px;
        line-height: 1.4;
    }

    .recommend .box .list {
        display: flex;
        flex-wrap: wrap;
    }

    .index-pv1 .box .list .list-item {
        flex: 0 0 ${var1}%;
        padding: 0 5px;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item-body {
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 5px;
        box-sizing: border-box;
        cursor: pointer;
    }

    .recommend .box .list img {
        width: 100%;
        height: 100px;
        display: block;
        margin: 0 auto;
        object-fit: cover;
        max-width: 100%;
    }

    .recommend .box .list .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        text-align: center;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item1 {
        flex: 0 0 100%;
    }

    .recommend .box .list .list-item2 {
        flex: 0 0 50%;
    }

    .recommend .box .list .list-item3 {
        flex: 0 0 33.33%;
    }

    .recommend .box .list .list-item4 {
        flex: 0 0 25%;
    }

    .recommend .box .list .list-item5 {
        flex: 0 0 25%;
    }

    /* 商品推荐-样式4-开始 */
    .recommend .list-4 {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .recommend .list-4 .list-4-body {
        display: flex;
        flex-direction: column;
    }

    .recommend .list-4 .list-4-item {
        position: relative;
        z-index: 1;
    }

    .recommend .list-4 .list-4-item.item-1 {
        width: 400px;
        height: 400px;
        margin-right: 20px;
    }

    .recommend .list-4 .list-4-item.item-2 {
        width: 220px;
        height: 190px;
        margin-right: 20px;
        margin-bottom: 20px;
    }

    .recommend .list-4 .list-4-item.item-3 {
        width: 220px;
        height: 190px;
        margin-right: 20px;
        margin-bottom: 0;
    }

    .recommend .list-4 .list-4-item.item-4 {
        width: 190px;
        height: 190px;
        margin-right: 0;
        margin-bottom: 20px;
    }

    .recommend .list-4 .list-4-item.item-5 {
        width: 190px;
        height: 190px;
        margin-right: 0;
        margin-bottom: 0;
    }

    .recommend .list-4 .list-4-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    .recommend .list-4 .list-4-item .list-4-item-center {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        display: flex;
        flex-wrap: wrap;
        background-color: rgba(0, 0, 0, .3);
    }

    .recommend .list-4 .list-4-item .list-4-item-center .list-4-item-title {
        width: 50%;
        text-align: left;
        line-height: 44px;
        color: #fff;
        font-size: 14px;
        padding: 0 6px;
    }

    .recommend .list-4 .list-4-item .list-4-item-center .list-4-item-price {
        width: 50%;
        text-align: right;
        line-height: 44px;
        color: #fff;
        font-size: 14px;
        padding: 0 6px;
    }

    /* 商品推荐-样式4-结束 */
    /* 商品推荐-样式5-开始 */
    .recommend #recommend-five-swiper.swiper-container-horizontal > .swiper-pagination-bullets {
        line-height: 1;
    }

    .recommend #recommend-five-swiper .swiper-slide.swiper-slide-prev {
        z-index: 5;
    }

    .recommend #recommend-five-swiper .swiper-slide.swiper-slide-next {
        z-index: 5;
    }

    .recommend #recommend-five-swiper .swiper-slide.swiper-slide-active {
        z-index: 9;
    }

    .recommend #lists-five-swiper.swiper-container-horizontal > .swiper-pagination-bullets {
        line-height: 1;
    }

    .recommend #lists-five-swiper .swiper-slide.swiper-slide-prev {
        z-index: 5;
    }

    .recommend #lists-five-swiper .swiper-slide.swiper-slide-next {
        z-index: 5;
    }

    .recommend #lists-five-swiper .swiper-slide.swiper-slide-active {
        z-index: 9;
    }

    /* 商品推荐-样式5-结束 */

    .news {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
        width: 100%;
    }

    .news .box {
        width: 1014px;
    }

    .news .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .news .box .title span {
        padding: 0 10px;
        line-height: 1.4;
    }

    .news .box .list {
        display: flex;
        flex-wrap: wrap;
    }

    .index-pv2 .box .list .list-item {
        flex: 0 0 25%;
        padding: 0 10px;
        box-sizing: border-box;
    }

    .news .box .list .list-item .list-item-body {
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        cursor: pointer;
    }

    .news .box .list .list-item .list-item-body img {
        width: 120px;
        height: 100%;
        display: block;
        margin: 0 auto;
        object-fit: cover;
        max-width: 100%;
    }

    .news .box .list .list-item .list-item-body .item-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding-left: 10px;
        box-sizing: border-box;
    }

    .news .box .list .list-item .list-item-body .item-info .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .news .box .list .list-item .list-item-body .item-info .time {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .news .box .list .list-item1 {
        flex: 0 0 100%;
    }

    .news .box .list .list-item2 {
        flex: 0 0 50%;
    }

    .news .box .list .list-item3 {
        flex: 0 0 25%;
    }

    .lists {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }

    .lists .box {
        width: 1014px;
        overflow: hidden;
    }

    .lists .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .lists .box .title span {
        padding: 0 10px;
        line-height: 1.4;
    }

    .lists .box .swiper-slide {
        box-sizing: border-box;
        cursor: pointer;
    }

    .lists .box .swiper-slide .img-box {
        width: 100%;
        overflow: hidden;
    }

    .lists .box .swiper-slide .img-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        max-width: 100%;
    }


    .index-pv1 .animation-box:hover {
        transform: perspective(1000px) translate3d(0px, -10px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
        transition: all 0.3s;
        z-index: 2;
        position: relative;
    }

    .index-pv2 .animation-box:hover {
        transform: perspective(1000px) translate3d(0px, -10px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
        transition: all 0.3s;
        z-index: 2;
        position: relative;
    }

    .index-pv3 .animation-box:hover {
        transform: perspective(1000px) translate3d(0px, -10px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
        transition: all 0.3s;
        z-index: 2;
        position: relative;
    }


    #new-list-6 .swiper-wrapper {
        -webkit-transition-timing-function: linear;
        -moz-transition-timing-function: linear;
        -ms-transition-timing-function: linear;
        -o-transition-timing-function: linear;
        transition-timing-function: linear;
    }

    /* 优雅内容区域样式 */
    #content {
        background: var(--backgroundColor);
        min-height: 100vh;
        padding: 32px 0;
    }

    /* 优雅区块样式 */
    .content-section {
        margin: 48px 0;
    }

    .section-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 24px;
    }

    /* 公告信息区域特殊样式 */
    .new-list-6 {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .new6-list {
        border-radius: 15px;
        background: #fff !important;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }

    .new6-list-item {
        transition: all 0.3s ease;
        border-radius: 8px;
        margin: 8px 10px !important;
        padding: 12px !important;
    }

    .new6-list-item:hover {
        background: var(--backgroundColor) !important;
        transform: translateX(5px);
    }

    /* 商品卡片样式优化 */
    .product-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
    }

    .product-card .product-overlay {
        pointer-events: none;
    }

    .product-card:hover .product-overlay {
        opacity: 1 !important;
    }

    .products-grid {
        animation: fadeInUp 0.6s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* 商品状态标签动画 */
    .product-card .product-image > div {
        animation: slideInLeft 0.3s ease-out;
    }

    @keyframes slideInLeft {
        from {
            opacity: 0;
            transform: translateX(-20px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }

    /* 筛选按钮样式 */
    .filter-btn:hover {
        background: var(--publicSubColor) !important;
        color: #fff !important;
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(56, 161, 105, 0.3);
    }

    .filter-btn.active {
        background: var(--publicSubColor) !important;
        color: #fff !important;
    }

    /* 商品筛选动画 */
    .product-card.filtered-out {
        opacity: 0;
        transform: scale(0.8);
        transition: all 0.3s ease;
    }

    .product-card.filtered-in {
        opacity: 1;
        transform: scale(1);
        transition: all 0.3s ease;
    }

    /* 响应式优化 */
    @media (max-width: 768px) {
        /* 轮播图响应式 */
        .hero-carousel {
            margin: 16px 0 !important;
            border-radius: 16px !important;
        }

        #test1 img {
            height: 350px !important;
        }

        .carousel-content {
            left: 30px !important;
            max-width: 300px !important;
        }

        .carousel-title {
            font-size: 28px !important;
        }

        .carousel-title span {
            font-size: 22px !important;
        }

        .carousel-description {
            font-size: 16px !important;
        }

        .carousel-actions {
            flex-direction: column !important;
            gap: 12px !important;
        }

        .hero-btn {
            width: 100% !important;
            justify-content: center !important;
        }

        .carousel-decoration {
            display: none !important;
        }

        .carousel-nav-prev,
        .carousel-nav-next {
            width: 40px !important;
            height: 40px !important;
        }

        .carousel-nav-prev {
            left: 15px !important;
        }

        .carousel-nav-next {
            right: 15px !important;
        }

        .hero-info-cards {
            grid-template-columns: 1fr !important;
            gap: 16px !important;
            margin-top: -20px !important;
        }

        .info-card {
            padding: 20px !important;
        }

        /* 其他内容响应式 */
        #content {
            padding: 20px 0;
        }

        .content-section {
            margin: 32px 0;
        }

        .section-container {
            padding: 0 16px;
        }

        .elegant-title h2 {
            font-size: 24px;
        }

        .products-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 16px;
            margin: 24px 0;
        }

        .product-card .product-info {
            padding: 16px;
        }

        .product-card img {
            height: 180px;
        }
    }

    @media (max-width: 480px) {
        /* 轮播图小屏幕优化 */
        #test1 img {
            height: 280px !important;
        }

        .carousel-content {
            left: 20px !important;
            right: 20px !important;
            max-width: none !important;
        }

        .carousel-title {
            font-size: 24px !important;
        }

        .carousel-title span {
            font-size: 18px !important;
        }

        .carousel-description {
            font-size: 14px !important;
        }

        .hero-btn {
            padding: 12px 20px !important;
            font-size: 14px !important;
        }

        .carousel-badge {
            font-size: 11px !important;
            padding: 4px 12px !important;
        }

        /* 其他内容小屏幕优化 */
        .products-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }

        .product-card img {
            height: 200px;
        }
    }
</style>
<body>
<div id="app">
    <!-- 现代化轮播图区域 -->
    <div class="hero-carousel-container" style="position: relative; margin: 0; background: linear-gradient(135deg, var(--secondaryBg) 0%, #e8f4f8 100%);">
        <div class="section-container" style="position: relative; z-index: 2;">
            <!-- 轮播图主体 -->
            <div class="hero-carousel" style="position: relative; margin: 32px 0; border-radius: 20px; overflow: hidden; box-shadow: 0 20px 60px rgba(0,0,0,0.1);">
                <div class="layui-carousel" id="test1" style="border-radius: 20px; overflow: hidden; position: relative;">
                    <div carousel-item>
                        <div v-for="(item,index) in swiperList" :key="index" class="carousel-slide" style="position: relative;">
                            <!-- 背景图片 -->
                            <img style="width: 100%; height: 500px; object-fit: cover;" :src="item.img"/>

                            <!-- 渐变遮罩 -->
                            <div class="carousel-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: linear-gradient(45deg, rgba(26, 54, 93, 0.7) 0%, rgba(56, 161, 105, 0.3) 100%);"></div>

                            <!-- 内容区域 -->
                            <div class="carousel-content" style="position: absolute; top: 50%; left: 60px; transform: translateY(-50%); color: #fff; z-index: 3; max-width: 500px;">
                                <div class="carousel-badge" style="display: inline-block; padding: 6px 16px; background: rgba(255, 255, 255, 0.2); border-radius: 20px; font-size: 12px; font-weight: 500; margin-bottom: 16px; backdrop-filter: blur(10px);">
                                    <i data-lucide="star" style="width: 12px; height: 12px; margin-right: 4px;"></i>
                                    精选推荐
                                </div>
                                <h1 class="carousel-title" style="font-size: 42px; font-weight: 700; margin: 0 0 16px 0; line-height: 1.2; text-shadow: 0 2px 10px rgba(0,0,0,0.3);">
                                    优质商品
                                    <span style="display: block; font-size: 32px; color: rgba(255,255,255,0.9);">精彩呈现</span>
                                </h1>
                                <p class="carousel-description" style="font-size: 18px; margin: 0 0 24px 0; opacity: 0.9; line-height: 1.5;">
                                    发现更多精选商品，享受优质购物体验
                                </p>
                                <div class="carousel-actions" style="display: flex; gap: 16px; align-items: center;">
                                    <button class="hero-btn primary" onclick="jump('../shangpin/list.html')" style="padding: 14px 28px; background: var(--publicSubColor); color: #fff; border: none; border-radius: 25px; font-size: 16px; font-weight: 600; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 8px;">
                                        <i data-lucide="shopping-bag" style="width: 18px; height: 18px;"></i>
                                        立即购买
                                    </button>
                                    <button class="hero-btn secondary" onclick="jump('../shangpin/list.html')" style="padding: 14px 28px; background: rgba(255,255,255,0.2); color: #fff; border: 1px solid rgba(255,255,255,0.3); border-radius: 25px; font-size: 16px; font-weight: 500; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(10px); display: flex; align-items: center; gap: 8px;">
                                        <i data-lucide="eye" style="width: 18px; height: 18px;"></i>
                                        了解更多
                                    </button>
                                </div>
                            </div>

                            <!-- 装饰元素 -->
                            <div class="carousel-decoration" style="position: absolute; top: 40px; right: 60px; z-index: 3;">
                                <div style="display: flex; flex-direction: column; gap: 12px; align-items: flex-end;">
                                    <div style="width: 60px; height: 60px; background: rgba(255,255,255,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px);">
                                        <i data-lucide="gift" style="width: 24px; height: 24px; color: #fff;"></i>
                                    </div>
                                    <div style="width: 40px; height: 40px; background: rgba(255,255,255,0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center; backdrop-filter: blur(10px);">
                                        <i data-lucide="heart" style="width: 18px; height: 18px; color: #fff;"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自定义导航按钮 -->
                <div class="carousel-nav-prev" style="position: absolute; left: 30px; top: 50%; transform: translateY(-50%); z-index: 4; width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(10px);">
                    <i data-lucide="chevron-left" style="width: 24px; height: 24px; color: #fff;"></i>
                </div>
                <div class="carousel-nav-next" style="position: absolute; right: 30px; top: 50%; transform: translateY(-50%); z-index: 4; width: 50px; height: 50px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; backdrop-filter: blur(10px);">
                    <i data-lucide="chevron-right" style="width: 24px; height: 24px; color: #fff;"></i>
                </div>
            </div>

            <!-- 轮播图下方信息卡片 -->
            <div class="hero-info-cards" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 24px; margin-top: -40px; position: relative; z-index: 5;">
                <div class="info-card elegant-card" style="padding: 24px; text-align: center; background: #fff;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--publicSubColor), #48bb78); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px auto;">
                        <i data-lucide="truck" style="width: 28px; height: 28px; color: #fff;"></i>
                    </div>
                    <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600; color: var(--textColor);">快速配送</h3>
                    <p style="margin: 0; color: var(--textSecondary); font-size: 14px; line-height: 1.5;">快速便捷的配送服务，让您快速收到心仪商品</p>
                </div>
                <div class="info-card elegant-card" style="padding: 24px; text-align: center; background: #fff;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--accentColor), #f56565); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px auto;">
                        <i data-lucide="shield-check" style="width: 28px; height: 28px; color: #fff;"></i>
                    </div>
                    <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600; color: var(--textColor);">品质保证</h3>
                    <p style="margin: 0; color: var(--textSecondary); font-size: 14px; line-height: 1.5;">严格质检，正品保障，让您购物更放心</p>
                </div>
                <div class="info-card elegant-card" style="padding: 24px; text-align: center; background: #fff;">
                    <div style="width: 60px; height: 60px; background: linear-gradient(135deg, var(--publicMainColor), #4a5568); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 16px auto;">
                        <i data-lucide="headphones" style="width: 28px; height: 28px; color: #fff;"></i>
                    </div>
                    <h3 style="margin: 0 0 8px 0; font-size: 18px; font-weight: 600; color: var(--textColor);">贴心服务</h3>
                    <p style="margin: 0; color: var(--textSecondary); font-size: 14px; line-height: 1.5;">7×24小时客服支持，随时为您解答疑问</p>
                </div>
            </div>
        </div>

        <!-- 背景装饰 -->
        <div class="hero-bg-decoration" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; z-index: 1; overflow: hidden;">
            <div style="position: absolute; top: 10%; right: 10%; width: 200px; height: 200px; background: radial-gradient(circle, rgba(56, 161, 105, 0.1) 0%, transparent 70%); border-radius: 50%;"></div>
            <div style="position: absolute; bottom: 20%; left: 5%; width: 150px; height: 150px; background: radial-gradient(circle, rgba(237, 137, 54, 0.1) 0%, transparent 70%); border-radius: 50%;"></div>
        </div>
    </div>

    <div id="content">
        <!-- 商品展示区域 -->
        <div class="content-section">
            <div class="section-container">
                <div class="elegant-title with-icon">
                    <h2><i data-lucide="package"></i>商品展示</h2>
                    <div class="subtitle">精选优质商品，为您提供最佳购物体验</div>
                </div>

                <!-- 商品统计信息 -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin: 24px 0; padding: 16px; background: var(--secondaryBg); border-radius: 12px;">
                    <div style="display: flex; gap: 24px;">
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: var(--publicSubColor);">{{shangpinList.length}}</div>
                            <div style="font-size: 14px; color: var(--textSecondary);">商品总数</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: var(--accentColor);">{{getHotProductsCount()}}</div>
                            <div style="font-size: 14px; color: var(--textSecondary);">热销商品</div>
                        </div>
                        <div style="text-align: center;">
                            <div style="font-size: 24px; font-weight: 700; color: var(--publicMainColor);">{{getDiscountProductsCount()}}</div>
                            <div style="font-size: 14px; color: var(--textSecondary);">特价商品</div>
                        </div>
                    </div>
                    <div style="color: var(--textSecondary); font-size: 13px;">
                        <i data-lucide="trending-up" style="width: 14px; height: 14px; margin-right: 4px;"></i>
                        实时更新
                    </div>
                </div>

                <!-- 商品筛选标签 -->
                <div class="product-filters" style="display: flex; justify-content: center; gap: 12px; margin: 24px 0; flex-wrap: wrap;">
                    <button class="filter-btn active" data-filter="all"
                            style="padding: 8px 16px; border: 1px solid var(--borderColor); background: var(--publicSubColor); color: #fff; border-radius: 20px; font-size: 14px; cursor: pointer; transition: all 0.2s ease; border: none;">
                        全部商品 ({{shangpinList.length}})
                    </button>
                    <button class="filter-btn" data-filter="hot"
                            style="padding: 8px 16px; border: 1px solid var(--borderColor); background: var(--backgroundColor); color: var(--textColor); border-radius: 20px; font-size: 14px; cursor: pointer; transition: all 0.2s ease;">
                        热销商品 ({{getHotProductsCount()}})
                    </button>
                    <button class="filter-btn" data-filter="new"
                            style="padding: 8px 16px; border: 1px solid var(--borderColor); background: var(--backgroundColor); color: var(--textColor); border-radius: 20px; font-size: 14px; cursor: pointer; transition: all 0.2s ease;">
                        新品上市 ({{getNewProductsCount()}})
                    </button>
                    <button class="filter-btn" data-filter="discount"
                            style="padding: 8px 16px; border: 1px solid var(--borderColor); background: var(--backgroundColor); color: var(--textColor); border-radius: 20px; font-size: 14px; cursor: pointer; transition: all 0.2s ease;">
                        特价商品 ({{getDiscountProductsCount()}})
                    </button>
                </div>
                <!-- 商品网格展示 -->
                <div class="products-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 24px; margin: 32px 0;">
                    <!-- 商品卡片 -->
                    <div v-for="(item, index) in shangpinList.slice(0, 8)" :key="index"
                         @click="jumpCheck('../shangpin/detail.html?id='+item.id, item.shangpinDelete, item.shangxiaTypes)"
                         class="product-card elegant-card"
                         style="cursor: pointer; transition: all 0.3s ease; position: relative; overflow: hidden;">

                        <!-- 商品图片 -->
                        <div class="product-image" style="position: relative; overflow: hidden;">
                            <img style="width: 100%; height: 220px; object-fit: cover; transition: transform 0.3s ease;"
                                 :src="item.shangpinPhoto ? item.shangpinPhoto.split(',')[0] : ''"
                                 :alt="item.shangpinName"
                                 @mouseover="$event.target.style.transform = 'scale(1.05)'"
                                 @mouseout="$event.target.style.transform = 'scale(1)'"/>

                            <!-- 商品状态标签 -->
                            <div v-if="item.shangxiaTypes == 2"
                                 style="position: absolute; top: 12px; left: 12px; background: var(--accentColor); color: #fff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                                已下架
                            </div>
                            <div v-else-if="item.shangpinKucunNumber <= 0"
                                 style="position: absolute; top: 12px; left: 12px; background: #666; color: #fff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                                售罄
                            </div>
                            <div v-else-if="index < 3"
                                 style="position: absolute; top: 12px; left: 12px; background: var(--publicSubColor); color: #fff; padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500;">
                                热销
                            </div>
                        </div>

                        <!-- 商品信息 -->
                        <div class="product-info" style="padding: 20px;">
                            <h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600; color: var(--textColor); line-height: 1.4; height: 44px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
                                {{item.shangpinName}}
                            </h3>

                            <!-- 商品价格 -->
                            <div style="margin: 12px 0; display: flex; align-items: baseline; gap: 8px;">
                                <span style="font-size: 20px; font-weight: 700; color: var(--accentColor);">
                                    ¥{{item.shangpinNewMoney}}
                                </span>
                                <span v-if="item.shangpinOldMoney && item.shangpinOldMoney != item.shangpinNewMoney"
                                      style="font-size: 14px; color: var(--textSecondary); text-decoration: line-through;">
                                    ¥{{item.shangpinOldMoney}}
                                </span>
                            </div>

                            <!-- 商品描述 -->
                            <p style="margin: 8px 0 12px 0; color: var(--textSecondary); font-size: 14px; line-height: 1.5; height: 42px; overflow: hidden; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">
                                {{item.shangpinContent || '暂无描述'}}
                            </p>

                            <!-- 商品底部信息 -->
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px; padding-top: 16px; border-top: 1px solid var(--borderColor);">
                                <div style="display: flex; align-items: center; gap: 4px; color: var(--textSecondary); font-size: 13px;">
                                    <i data-lucide="package" style="width: 14px; height: 14px;"></i>
                                    库存: {{item.shangpinKucunNumber || 0}}
                                </div>
                                <div style="display: flex; align-items: center; gap: 4px; color: var(--publicSubColor); font-size: 13px; font-weight: 500;">
                                    <i data-lucide="eye" style="width: 14px; height: 14px;"></i>
                                    查看详情
                                </div>
                            </div>
                        </div>

                        <!-- 悬停遮罩 -->
                        <div class="product-overlay"
                             style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(56, 161, 105, 0.9); opacity: 0; transition: opacity 0.3s ease; display: flex; align-items: center; justify-content: center;"
                             @mouseover="$event.target.style.opacity = '1'"
                             @mouseout="$event.target.style.opacity = '0'">
                            <div style="text-align: center; color: #fff;">
                                <i data-lucide="shopping-cart" style="width: 32px; height: 32px; margin-bottom: 8px;"></i>
                                <div style="font-size: 16px; font-weight: 600;">立即查看</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 32px;">
                    <button @click="jump('../shangpin/list.html')" class="elegant-btn" type="button">
                        <i data-lucide="arrow-right" style="width: 16px; height: 16px;"></i>
                        查看更多商品
                    </button>
                </div>
            </div>
        </div>
        <div class="news index-pv2" style="display: flex;justify-content: center;width:100%"
            :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px 0 0 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 0, 0, 0)","borderRadius":"0","borderWidth":"0","borderStyle":"solid"}'>
            <div class="box" style='width:90%; max-width: 1200px;'>
                <div class="modern-title">
                    <h2><i class="fas fa-bullhorn" style="margin-right: 10px; color: var(--publicSubColor);"></i>公告信息</h2>
                    <div class="subtitle">最新动态与重要通知，第一时间为您推送</div>
                </div>
                <div v-if="gonggaoList.length" class="new-list-6" style="display: flex;">
                    <div class="swiper-container" id="new-list-6"
                         :style='{"padding":"0","boxShadow":"0 0 0px rgba(0,0,0, .3)","margin":"0","backgroundColor":"var(--publicSubColor)","borderRadius":"0","borderWidth":"0","width":"60%","borderStyle":"solid","height":"250px"}'>
                        <div class="swiper-wrapper">
                            <div class="swiper-slide animation-box" v-for="(item,index) in gonggaoList" v-if="index<5"
                                 :key="index" @click="jump('../gonggao/detail.html?id='+item.id)">
                                <img :style='{"padding":"6px","boxShadow":"0 0 0px rgba(0,0,0, .3)","backgroundColor":"var(--publicSubColor)","borderRadius":"20px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}'
                                     style="object-fit: cover;width: 100%;height: 100%;box-sizing: border-box;"
                                     :src="item.gonggaoPhoto">
                            </div>
                        </div>
                    </div>
                    <div class="new6-list" style="width: 40%;"
                         :style='{"padding":"10px","boxShadow":"0 0 6px rgba(0,0,0, .3)","margin":"0","borderColor":"#ccc","backgroundColor":"var(--publicSubColor)","overflow":"hidden","borderRadius":"0","borderWidth":"0","width":"40%","boxSizing":"border-box","borderStyle":"solid","height":"250px"}'>
                        <div class="gonggao-title-box"
                             style="display: flex;justify-content: space-between;align-items: center;">
                            <div class="new-title"
                                 :style='{"padding":"0 10px","boxShadow":"0 0 0px rgba(0,0,0,.3)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(43,135,33,0)","color":"#fff","borderRadius":"0","borderWidth":"0","width":"auto","lineHeight":"28px","fontSize":"14px","borderStyle":"solid"}'>
                                最新动态
                            </div>
                        </div>
                        <div v-for="(item,index) in gonggaoList" v-if="index<5" :key="index"
                             @click="jump('../gonggao/detail.html?id='+item.id)" class="new6-list-item"
                             style="display: flex;box-sizing: border-box;" style="box-sizing: border-box;"
                             :style='{"padding":"0 10px","boxShadow":"0 0 3px rgba(0,0,0,0)","margin":"0 0 5px 0","borderColor":"#ccc","backgroundColor":"#fff","borderRadius":"0","borderWidth":"0 0 1px 0","width":"100%","borderStyle":"solid","height":"auto"}'>
                            <div class="new6-list-item-title"
                                 :style='{"padding":"0","boxShadow":"0 0 0px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"var(--publicSubColor)","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"70%","lineHeight":"28px","fontSize":"14px","borderStyle":"solid"}'>
                                {{ item.gonggaoName }}
                            </div>
                            <div class="new6-list-item-time"
                                 :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"rgba(153, 153, 153, 1)","textAlign":"right","borderRadius":"0","borderWidth":"0","width":"30%","lineHeight":"28px","fontSize":"12px","borderStyle":"solid"}'>
                                {{ item.insertTime }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clear"></div>
                <div style="text-align: center; margin-top: 30px;">
                    <button @click="jump('../gonggao/list.html')" class="modern-btn" type="button">
                        <i class="fas fa-arrow-right"></i>
                        查看更多公告
                    </button>
                </div>
            </div>
        </div>
        <div class="recommend index-pv3"
             :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px 0 0 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 0, 0, 0)","borderRadius":"4px","borderWidth":"0","borderStyle":"solid"}'>
            <div class="box" style='width:90%; max-width: 1200px;'>
                <div class="modern-title">
                    <h2><i class="fas fa-store" style="margin-right: 10px; color: var(--publicSubColor);"></i>商家展示</h2>
                    <div class="subtitle">优质商家推荐，值得信赖的合作伙伴</div>
                </div>
                <div class="list-4">
                    <div class="list-4-body-left"
                         style="display: flex;flex-direction: column;transform: none !important">
                        <div class="list-4-body" style="display: flex;flex-direction: row;">
                            <div v-if="shangjiaList.length>0"
                                @click="jump('../shangjia/detail.html?id='+shangjiaList[0].id)"
                                class="list-4-item animation-box item-1"
                                :style='{"padding":"0","boxShadow":"0 0 6px rgba(2, 93, 172, 1)","margin":"0 20px 20px 0","borderColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"390px","borderStyle":"solid","height":"250px"}'>
                                <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}'
                                     :src="shangjiaList[0].shangjiaPhoto?shangjiaList[0].shangjiaPhoto.split(',')[0]:''"
                                     alt=""/>
                                <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0 0 10px 10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}'
                                     v-if="true" class="list-4-item-center">
                                    <div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"100%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}'
                                         class="list-4-item-title">{{shangjiaList[0].shangjiaName}}
                                    </div>
                                </div>
                            </div>
                                <div v-if="shangjiaList.length>1"
                                @click="jump('../shangjia/detail.html?id='+shangjiaList[1].id)"
                                class="list-4-item animation-box item-2"
                                :style='{"padding":"0","boxShadow":"0 0 6px rgba(2, 93, 172, 1)","margin":"0 20px 20px 0","borderColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"390px","borderStyle":"solid","height":"250px"}'>
                                <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}'
                                     :src="shangjiaList[1].shangjiaPhoto?shangjiaList[1].shangjiaPhoto.split(',')[0]:''"
                                     alt=""/>
                                <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0 0 10px 10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}'
                                     v-if="true" class="list-4-item-center">
                                    <div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"100%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}'
                                         class="list-4-item-title">{{shangjiaList[1].shangjiaName}}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="shangjiaList.length>2"
                                @click="jump('../shangjia/detail.html?id='+shangjiaList[2].id)"
                                class="list-4-item animation-box item-3"
                                :style='{"padding":"0","boxShadow":"0 0 6px rgba(2, 93, 172, 1)","margin":"0 20px 0 0","borderColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"800px","borderStyle":"solid","height":"280px"}'>
                            <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}'
                                 :src="shangjiaList[2].shangjiaPhoto?shangjiaList[2].shangjiaPhoto.split(',')[0]:''"
                                 alt=""/>
                            <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0 0 10px 10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}'
                                 v-if="true" class="list-4-item-center">
                                <div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"100%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}'
                                     class="list-4-item-title">{{shangjiaList[2].shangjiaName}}
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="list-4-body"  style="display: flex;flex-direction: row;margin-top: 10px">
                        <div v-if="shangjiaList.length>3"
                                @click="jump('../shangjia/detail.html?id='+shangjiaList[3].id)"
                                class="list-4-item animation-box item-4"
                                :style='{"padding":"0","boxShadow":"0 0 6px rgba(2, 93, 172, 1)","margin":"0 20px 20px 0","borderColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"390px","borderStyle":"solid","height":"250px"}'>
                            <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}'
                                 :src="shangjiaList[3].shangjiaPhoto?shangjiaList[3].shangjiaPhoto.split(',')[0]:''"
                                 alt=""/>
                            <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0 0 10px 10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}'
                                 v-if="true" class="list-4-item-center">
                                <div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"100%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}'
                                     class="list-4-item-title">{{shangjiaList[3].shangjiaName}}
                                </div>
                            </div>
                        </div>
                        <div v-if="shangjiaList.length>4"
                                @click="jump('../shangjia/detail.html?id='+shangjiaList[4].id)"
                                class="list-4-item animation-box item-5"
                                :style='{"padding":"0","boxShadow":"0 0 6px rgba(2, 93, 172, 1)","margin":"0 20px 20px 0","borderColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"390px","borderStyle":"solid","height":"250px"}'>
                            <img :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"#ccc","borderRadius":"10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"100%"}'
                                 :src="shangjiaList[4].shangjiaPhoto?shangjiaList[4].shangjiaPhoto.split(',')[0]:''"
                                 alt=""/>
                            <div :style='{"padding":"0","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,.3)","borderRadius":"0 0 10px 10px","borderWidth":"0","width":"100%","borderStyle":"solid","height":"auto"}'
                                 v-if="true" class="list-4-item-center">
                                <div :style='{"padding":"0 6px","boxShadow":"0 0 6px rgba(0,0,0,0)","margin":"0","borderColor":"#ccc","backgroundColor":"rgba(0,0,0,0)","color":"#fff","textAlign":"left","borderRadius":"0","borderWidth":"0","width":"100%","lineHeight":"44px","fontSize":"14px","borderStyle":"solid"}'
                                     class="list-4-item-title">{{shangjiaList[4].shangjiaName}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="clear"></div>
                <div style="text-align: center; margin-top: 30px;">
                    <button @click="jump('../shangjia/list.html')" class="modern-btn" type="button">
                        <i class="fas fa-arrow-right"></i>
                        查看更多商家
                    </button>
                </div>
            </div>
        </div>

    </div>
</div>

<script src="../../xznstatic/js/bootstrap.min.js" type="text/javascript" charset="utf-8"></script>
<script src="../../layui/layui.js"></script>
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">
<script src="../../xznstatic/js/swiper.min.js"></script>
<script src="../../js/config.js"></script>
<script src="../../modules/config.js"></script>
<script src="../../js/utils.js"></script>
<script type="text/javascript">
    var vue = new Vue({
        el: '#app',
        data: {
            swiperList: [],
            // dianyingRecommend: [],
            shangpinList: [],
            gonggaoList: [],
            shangjiaList: [],
        },
        filters: {
            newsDesc: function (val) {
                if (val) {
                    val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
                    if (val.length > 60) {
                        val = val.substring(0, 60);
                    }

                    return val;
                }
                return '';
            }
        },
        mounted() {
            // 初始化Lucide图标
            this.$nextTick(() => {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                }
                // 初始化筛选按钮事件
                this.initFilterButtons();
                // 初始化轮播图导航按钮
                this.initCarouselNavigation();
            });
        },
        methods: {
            jump(url) {
                jump(url)
            }
            ,jumpCheck(url,check1,check2) {
                if(check1 == "2" || check1 == 2){//级联表的逻辑删除字段[1:未删除 2:已删除]
                    layui.layer.msg("已经被删除", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                if(check2 == "2"  || check2 == 2){//是否下架[1:上架 2:下架]
                    layui.layer.msg("已经下架", {
                        time: 2000,
                        icon: 2
                    });
                    return false;
                }
                this.jump(url);
            },
            // 初始化筛选按钮
            initFilterButtons() {
                const filterBtns = document.querySelectorAll('.filter-btn');
                filterBtns.forEach(btn => {
                    btn.addEventListener('click', (e) => {
                        // 移除所有按钮的active类
                        filterBtns.forEach(b => {
                            b.classList.remove('active');
                            b.style.background = 'var(--backgroundColor)';
                            b.style.color = 'var(--textColor)';
                        });

                        // 给当前按钮添加active类
                        e.target.classList.add('active');
                        e.target.style.background = 'var(--publicSubColor)';
                        e.target.style.color = '#fff';

                        // 执行筛选
                        this.filterProducts(e.target.dataset.filter);
                    });
                });
            },
            // 商品筛选逻辑
            filterProducts(filter) {
                const productCards = document.querySelectorAll('.product-card');

                productCards.forEach((card, index) => {
                    const item = this.shangpinList[index];
                    let shouldShow = true;

                    switch(filter) {
                        case 'hot':
                            shouldShow = index < 3; // 前3个为热销
                            break;
                        case 'new':
                            shouldShow = index >= 3 && index < 6; // 中间3个为新品
                            break;
                        case 'discount':
                            shouldShow = item && item.shangpinOldMoney && item.shangpinOldMoney != item.shangpinNewMoney;
                            break;
                        case 'all':
                        default:
                            shouldShow = true;
                            break;
                    }

                    if (shouldShow) {
                        card.style.display = 'block';
                        card.classList.remove('filtered-out');
                        card.classList.add('filtered-in');
                    } else {
                        card.classList.remove('filtered-in');
                        card.classList.add('filtered-out');
                        setTimeout(() => {
                            if (card.classList.contains('filtered-out')) {
                                card.style.display = 'none';
                            }
                        }, 300);
                    }
                });
            },
            // 获取热销商品数量
            getHotProductsCount() {
                return Math.min(3, this.shangpinList.length);
            },
            // 获取新品数量
            getNewProductsCount() {
                return Math.min(3, Math.max(0, this.shangpinList.length - 3));
            },
            // 获取特价商品数量
            getDiscountProductsCount() {
                return this.shangpinList.filter(item =>
                    item.shangpinOldMoney && item.shangpinOldMoney != item.shangpinNewMoney
                ).length;
            },
            // 初始化轮播图导航按钮
            initCarouselNavigation() {
                const prevBtn = document.querySelector('.carousel-nav-prev');
                const nextBtn = document.querySelector('.carousel-nav-next');

                if (prevBtn && nextBtn) {
                    prevBtn.addEventListener('click', () => {
                        // 触发layui轮播图的上一张
                        const carousel = layui.carousel;
                        if (carousel) {
                            carousel.reload('test1', {
                                elem: '#test1'
                            });
                        }
                        // 手动触发上一张
                        this.carouselPrev();
                    });

                    nextBtn.addEventListener('click', () => {
                        // 触发layui轮播图的下一张
                        const carousel = layui.carousel;
                        if (carousel) {
                            carousel.reload('test1', {
                                elem: '#test1'
                            });
                        }
                        // 手动触发下一张
                        this.carouselNext();
                    });
                }
            },
            // 轮播图上一张
            carouselPrev() {
                const indicators = document.querySelectorAll('#test1 .layui-carousel-ind li');
                const current = document.querySelector('#test1 .layui-carousel-ind li.layui-this');
                if (current && indicators.length > 0) {
                    const currentIndex = Array.from(indicators).indexOf(current);
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : indicators.length - 1;
                    indicators[prevIndex].click();
                }
            },
            // 轮播图下一张
            carouselNext() {
                const indicators = document.querySelectorAll('#test1 .layui-carousel-ind li');
                const current = document.querySelector('#test1 .layui-carousel-ind li.layui-this');
                if (current && indicators.length > 0) {
                    const currentIndex = Array.from(indicators).indexOf(current);
                    const nextIndex = currentIndex < indicators.length - 1 ? currentIndex + 1 : 0;
                    indicators[nextIndex].click();
                }
            }
        }
    });

    layui.use(['layer', 'form', 'element', 'carousel', 'http', 'jquery'], function () {
        var layer = layui.layer;
        var element = layui.element;
        var form = layui.form;
        var carousel = layui.carousel;
        var http = layui.http;
        var jquery = layui.jquery;

        // 获取轮播图 数据
        http.request('config/list', 'get', {
            page: 1,
            limit: 5
        }, function (res) {
            if (res.data.list.length > 0) {
                let swiperList = [];
                res.data.list.forEach(element => {
                    if(element.value != null
            )
                {
                    swiperList.push({
                        img: element.value
                    });
                }
            })
                ;

                vue.swiperList = swiperList;

                vue.$nextTick(() => {
                    carousel.render({
                    elem: '#test1',
                    width: '100%',
                    height: '450px',
                    arrow: 'hover',
                    anim: 'default',
                    autoplay: 'true',
                    interval: '3000',
                    indicator: 'inside'
                });

            })

                // vue.$nextTick(()=>{
                //   window.xznSlide();
                // });
            }
        });

        http.request('shangpin/list', 'get', {
            page: 1,
            limit: 8,
            shangxiaTypes: 1,
            shangpinDelete: 1,
        }, function (res) {
            vue.shangpinList = res.data.list;
            let flag = 6;
            let options = {
                "navigation": {"nextEl": ".swiper-button-next", "prevEl": ".swiper-button-prev"},
                "slidesPerView": 5,
                "loop": true,
                "spaceBetween": 20,
                "autoplay": {"delay": 3000, "disableOnInteraction": false}
            }
            options.pagination = {el: 'null'}
            if (flag == 3) {
                vue.$nextTick(() => {
                    new Swiper('#newsnews', options)
                })
            }
            if (flag == 6) {
                let sixSwiper = {
                    loop: true,
                    speed: 2500,
                    slidesPerView: 3,
                    spaceBetween: 20,
                    centeredSlides: true,
                    watchSlidesProgress: true,
                    autoplay: {
                        delay: 0,
                        stopOnLastSlide: false,
                        disableOnInteraction: false
                    }
                }

                vue.$nextTick(() => {
                    new Swiper('#new-list-6', sixSwiper)
                })
            }

        });
        http.request('gonggao/list', 'get', {
            page: 1,
            limit: 8,
        }, function (res) {
            vue.gonggaoList = res.data.list;
            let flag = 6;
            let options = {
                "navigation": {"nextEl": ".swiper-button-next", "prevEl": ".swiper-button-prev"},
                "slidesPerView": 5,
                "loop": true,
                "spaceBetween": 20,
                "autoplay": {"delay": 3000, "disableOnInteraction": false}
            }
            options.pagination = {el: 'null'}
            if (flag == 3) {
                vue.$nextTick(() => {
                    new Swiper('#newsnews', options)
                })
            }
            if (flag == 6) {
                let sixSwiper = {
                    loop: true,
                    speed: 2500,
                    slidesPerView: 3,
                    spaceBetween: 20,
                    centeredSlides: true,
                    watchSlidesProgress: true,
                    autoplay: {
                        delay: 0,
                        stopOnLastSlide: false,
                        disableOnInteraction: false
                    }
                }

                vue.$nextTick(() => {
                    new Swiper('#new-list-6', sixSwiper)
                })
            }

        });
        http.request('shangjia/list', 'get', {
            page: 1,
            limit: 8,
            shangjiaDelete: 1,
        }, function (res) {
            vue.shangjiaList = res.data.list;
            let flag = 6;
            let options = {
                "navigation": {"nextEl": ".swiper-button-next", "prevEl": ".swiper-button-prev"},
                "slidesPerView": 5,
                "loop": true,
                "spaceBetween": 20,
                "autoplay": {"delay": 3000, "disableOnInteraction": false}
            }
            options.pagination = {el: 'null'}
            if (flag == 3) {
                vue.$nextTick(() => {
                    new Swiper('#newsnews', options)
                })
            }
            if (flag == 6) {
                let sixSwiper = {
                    loop: true,
                    speed: 2500,
                    slidesPerView: 3,
                    spaceBetween: 20,
                    centeredSlides: true,
                    watchSlidesProgress: true,
                    autoplay: {
                        delay: 0,
                        stopOnLastSlide: false,
                        disableOnInteraction: false
                    }
                }

                vue.$nextTick(() => {
                    new Swiper('#new-list-6', sixSwiper)
                })
            }

        });



    });

    window.xznSlide = function () {
        // jQuery(".banner").slide({mainCell:".bd ul",autoPlay:true,interTime:5000});
        jQuery("#ifocus").slide({
            titCell: "#ifocus_btn li",
            mainCell: "#ifocus_piclist ul",
            effect: "leftLoop",
            delayTime: 200,
            autoPlay: true,
            triggerTime: 0
        });
        jQuery("#ifocus").slide({titCell: "#ifocus_btn li", mainCell: "#ifocus_tx ul", delayTime: 0, autoPlay: true});
        jQuery(".product_list").slide({
            mainCell: ".bd ul",
            autoPage: true,
            effect: "leftLoop",
            autoPlay: true,
            vis: 5,
            trigger: "click",
            interTime: 4000
        });
    };
</script>
<script src="../../xznstatic/js/index.js"></script>
</body>
</html>
